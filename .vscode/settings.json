{
  // JS
  "javascript.updateImportsOnFileMove.enabled": "always",
  // JSON
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  // VSCode Editor
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never",
    "source.fixAll.stylelint": "explicit"
  },
  // Extension: Git
  "git.rebaseWhenSync": true,
  "git.autofetch": true,
  "git.enableSmartCommit": true,
  // Extension: Prettier
  "prettier.requireConfig": true,
  // Extension: ESLint
  "eslint.validate": [
    "javascript",
    "typescript",
    "javascriptreact",
    "typescriptreact"
  ],
  "eslint.format.enable": true,
  // Stylelint
  "css.validate": false,
  "less.validate": false,
  "scss.validate": false,
  "stylelint.enable": true,
  "stylelint.validate": [
    "css",
    "scss",
    "typescript",
    "typescriptreact"
  ],
  "markdown.extension.toc.updateOnSave": false,
  "files.insertFinalNewline": true,
  "editor.linkedEditing": true,
  "typescript.tsdk": "node_modules/typescript/lib",
  "cSpell.words": [
    "iconify"
  ]
}
