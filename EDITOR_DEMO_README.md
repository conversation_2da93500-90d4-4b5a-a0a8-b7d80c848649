# Rich Text Editor Demo

This demo showcases the Rich Text Editor component built with React Quill and integrated with S3 image upload functionality.

## Demo Page

Visit the demo at: `http://localhost:3001/editor-demo`

## Features Demonstrated

### 1. Basic Editor
- Full-featured rich text editor with toolbar
- Real-time HTML output display
- Standard formatting options (bold, italic, underline)
- Headers (H2, H3, H4)
- Lists (ordered and bullet)
- Links and images
- Code and code blocks

### 2. Optional Toolbar Editor
- Toolbar appears only when the editor is focused
- Useful for inline editing scenarios
- Same functionality as basic editor

### 3. Custom Styled Editor
- Demonstrates custom container styling
- Shows how to apply custom CSS classes
- Blue-themed styling example

## Technical Implementation

### Files Created/Modified

1. **`src/pages/editor-demo.tsx`** - Main demo page
2. **`src/components/EditorDemo.tsx`** - Editor component with mock S3 upload
3. **`src/hooks/useS3ImageUploadMock.ts`** - Mock implementation of S3 upload hook

### Key Features

- **SSR Safe**: Uses dynamic imports to prevent server-side rendering issues
- **Mock Image Upload**: Simulates S3 upload with placeholder images
- **Responsive Design**: Works on different screen sizes
- **Real-time Preview**: Shows HTML output as you type
- **Error Handling**: Graceful handling of upload failures

### Image Upload Demo

The demo includes a mock image upload feature that:
- Validates file types (PNG, JPG, JPEG, GIF, WebP)
- Checks file size (max 5MB)
- Simulates upload delay (2 seconds)
- Returns placeholder images from picsum.photos
- Shows loading state during upload
- Handles errors gracefully

## How to Test

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Navigate to the demo page**:
   Open `http://localhost:3001/editor-demo` in your browser

3. **Test the features**:
   - Type and format text using toolbar buttons
   - Try image upload by clicking the image button
   - Focus on the second editor to see optional toolbar
   - Check HTML output below each editor
   - Create lists, add links, and use code blocks

## Integration Notes

To integrate this editor into your application:

1. **Use the original Editor component** (`src/components/Editor.tsx`) for production
2. **Configure real S3 upload** by implementing the actual `useS3ImageUpload` hook
3. **Add proper error handling** for production scenarios
4. **Customize styling** by modifying the CSS classes in `src/styles/globals.css`

## Dependencies

- `react-quill`: Rich text editor component
- `quill-resize-image`: Image resizing functionality
- `@tanstack/react-query`: Data fetching and caching
- `next/dynamic`: Dynamic imports for SSR safety

## Troubleshooting

If you encounter issues:

1. **SSR Errors**: The editor uses dynamic imports to prevent SSR issues
2. **Image Upload**: Mock implementation returns placeholder images
3. **Styling**: Editor styles are defined in global CSS file
4. **Performance**: Large images are automatically resized

## Next Steps

- Implement real S3 integration
- Add more formatting options
- Enhance image handling
- Add collaborative editing features
- Implement auto-save functionality
