import React, { useState } from 'react'

import { TextField, IconButton, InputAdornment } from '@mui/material'

interface PasswordInputProps {
  label: string
  error?: boolean
  helperText?: string
  value: string
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void
}

const PasswordInput: React.FC<PasswordInputProps> = ({ label, error, helperText, value, onChange }) => {
  const [showPassword, setShowPassword] = useState(false)

  const togglePasswordVisibility = () => setShowPassword(prev => !prev)

  return (
    <TextField
      size='small'
      type={showPassword ? 'text' : 'password'}
      label={label}
      value={value}
      onChange={onChange}
      error={error}
      helperText={helperText}
      fullWidth
      InputProps={{
        endAdornment: (
          <InputAdornment position='end'>
            <IconButton onClick={togglePasswordVisibility} edge='end'>
              <i className={showPassword ? 'ri-eye-line' : 'ri-eye-off-line'} />
            </IconButton>
          </InputAdornment>
        )
      }}
    />
  )
}

export default PasswordInput
