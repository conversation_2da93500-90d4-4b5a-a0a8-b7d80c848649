'use client'
import React, { useEffect, useRef, useState } from 'react'
import dynamic from 'next/dynamic'

import { useS3ImageUpload } from '@/hooks/useS3ImageUploadMock'

// Dynamically import ReactQuill to prevent SSR issues
const ReactQuill = dynamic(() => import('react-quill'), {
  ssr: false,
  loading: () => <div className="editor-loading p-4 text-center text-gray-500">Loading editor...</div>
})

// Import Quill only on client side
let Quill: any = null

// Import Quill and QuillResizeImage only on client side
let QuillResizeImage: any = null

if (typeof window !== 'undefined') {
  Quill = require('react-quill').Quill
  QuillResizeImage = require('quill-resize-image')
  require('react-quill/dist/quill.snow.css')
}

interface EditorProps {
  value?: string
  onChange?: (value: string) => void
  onEditorRef?: (instance: any) => void
  containerClassName?: string
  className?: string
  placeholder?: string
  optionalToolbar?: boolean
}

const EditorDemo = ({
  value,
  onChange,
  onEditorRef,
  containerClassName = '',
  className = '',
  placeholder = '',
  optionalToolbar = false
}: EditorProps) => {
  const quillRef = useRef<any>(null)
  const { uploadImage } = useS3ImageUpload()
  const [isClient, setIsClient] = useState(false)

  // Ensure component only renders on client side
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Only access icons if Quill is available
  useEffect(() => {
    if (typeof window !== 'undefined' && Quill) {
      const icons = Quill.import('ui/icons')

      icons['code-block'] =
        '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256"><rect width="256" height="256" fill="none"/><polyline points="64 32 32 64 64 96" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/><polyline points="104 32 136 64 104 96" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/><path d="M176,48h24a8,8,0,0,1,8,8V200a8,8,0,0,1-8,8H56a8,8,0,0,1-8-8V136" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/></svg>'
    }
  }, [])

  useEffect(() => {
    if (quillRef.current && onEditorRef) {
      onEditorRef(quillRef.current)
    }
  }, [onEditorRef])
  const [isEditorVisible, setIsEditorVisible] = useState(optionalToolbar ? false : true)

  // Register modules only on client side
  useEffect(() => {
    if (typeof window !== 'undefined' && Quill && QuillResizeImage) {
      Quill.register('modules/resize', QuillResizeImage)
    }
  }, [])

  useEffect(() => {
    if (quillRef.current) {
      const container = quillRef.current.editor?.root?.parentElement

      if (container) {
        container.className = `${container.className} ${containerClassName}`
      }
    }
  }, [quillRef, containerClassName])

  // Custom image handler for S3 upload
  const imageHandler = () => {
    if (typeof window === 'undefined' || typeof document === 'undefined') return

    const input = document.createElement('input')

    input.setAttribute('type', 'file')
    input.setAttribute('accept', 'image/*')
    input.click()

    input.onchange = async () => {
      if (input.files && input.files[0]) {
        const file = input.files[0]

        try {
          // Show loading state
          const quill = quillRef.current?.getEditor()

          if (quill) {
            const range = quill.getSelection(true) || { index: 0, length: 0 }

            // Insert a placeholder while uploading
            quill.insertText(range.index, '📸 Uploading image...', 'user')

            // Upload image to S3 (mocked)
            const imageUrl = await uploadImage(file)

            // Remove loading text and insert image
            quill.deleteText(range.index, '📸 Uploading image...'.length)
            quill.insertEmbed(range.index, 'image', imageUrl, 'user')
            quill.setSelection(range.index + 1, 0, 'user')
          }
        } catch (error) {
          console.error('Image upload failed:', error)

          // Remove loading text and show error
          const quill = quillRef.current?.getEditor()

          if (quill) {
            const range = quill.getSelection(true) || { index: 0, length: 0 }

            quill.deleteText(range.index, '📸 Uploading image...'.length)
            quill.insertText(
              range.index,
              `❌ Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
              'user'
            )
          }
        }
      }
    }
  }

  // Modules configuration - only include resize if available
  const modules = {
    toolbar: {
      container: [
        [{ header: [2, 3, 4, false] }],
        ['bold', 'italic', 'underline'],
        [{ list: 'ordered' }, { list: 'bullet' }],
        ['link', 'image', 'code', 'code-block']
      ],
      handlers: {
        image: imageHandler
      }
    },
    ...(typeof window !== 'undefined' && Quill && QuillResizeImage
      ? {
          resize: {
            locale: {}
          }
        }
      : {})
  }

  const formats = ['header', 'bold', 'italic', 'underline', 'list', 'bullet', 'link', 'image', 'code', 'code-block']

  // Create a wrapper component to handle the ref properly
  const QuillWrapper = ({ ...props }: any) => {
    return (
      <ReactQuill
        {...props}
        ref={(el: any) => {
          quillRef.current = el

          if (onEditorRef && el) {
            onEditorRef(el)
          }
        }}
      />
    )
  }

  // Don't render on server side
  if (!isClient) {
    return <div className="editor-loading p-4 text-center text-gray-500">Loading editor...</div>
  }

  return (
    <div
      className={`custom-quill-editor ${className} ${optionalToolbar && !isEditorVisible ? 'optional-toolbar' : ''}`}
    >
      <QuillWrapper
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        modules={modules}
        formats={formats}
        theme='snow'
        onFocus={() => setIsEditorVisible(true)}
        onBlur={() => setIsEditorVisible(false)}
      />
    </div>
  )
}

export default EditorDemo
