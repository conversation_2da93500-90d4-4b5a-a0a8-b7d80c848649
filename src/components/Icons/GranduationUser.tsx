import React from 'react'

interface IconProps extends React.SVGProps<SVGSVGElement> {
  width?: string | number
  height?: string | number
  fill?: string
  className?: string
}

const UserIcon: React.FC<IconProps> = ({
  width = 20,
  height = 20,
  fill = 'currentColor',
  className = '',
  ...props
}) => (
  <svg
    viewBox='0 0 24 24'
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    fill={fill}
    className={className}
    {...props}
  >
    <path d='M18 10.5V6L15.89 7.06C15.96 7.36 16 7.67 16 8C16 10.21 14.21 12 12 12C9.79 12 8 10.21 8 8C8 7.67 8.04 7.36 8.11 7.06L5 5.5L12 2L19 5.5V10.5H18M12 9L10 8C10 9.1 10.9 10 12 10C13.1 10 14 9.1 14 8L12 9M14.75 5.42L12.16 4.1L9.47 5.47L12.07 6.79L14.75 5.42M12 13C14.67 13 20 14.33 20 17V20H4V17C4 14.33 9.33 13 12 13M12 14.9C9 14.9 5.9 16.36 5.9 17V18.1H18.1V17C18.1 16.36 14.97 14.9 12 14.9Z'></path>
  </svg>
)

export default UserIcon
