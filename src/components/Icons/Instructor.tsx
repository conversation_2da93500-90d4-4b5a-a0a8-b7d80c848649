import React from 'react'

interface IconProps extends React.SVGProps<SVGSVGElement> {
  width?: string | number
  height?: string | number
  fill?: string
  className?: string
}

const InstructorIcon: React.FC<IconProps> = ({
  width = 20,
  height = 20,
  fill = 'currentColor',
  className = '',
  ...props
}) => (
  <svg
    viewBox='0 0 24 24'
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    fill={fill}
    className={className}
    {...props}
  >
    <path d='M20,17A2,2 0 0,0 22,15V4A2,2 0 0,0 20,2H9.46C9.81,2.61 10,3.3 10,4H20V15H11V17M15,7V9H9V22H7V16H5V22H3V14H1.5V9A2,2 0 0,1 3.5,7H15M8,4A2,2 0 0,1 6,6A2,2 0 0,1 4,4A2,2 0 0,1 6,2A2,2 0 0,1 8,4Z'></path>
  </svg>
)

export default InstructorIcon
