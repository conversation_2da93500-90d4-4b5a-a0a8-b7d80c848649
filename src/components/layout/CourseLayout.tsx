// React Imports
import type { ReactNode } from 'react'

// Third-party Imports
import classnames from 'classnames'

// Type Imports
import type { ChildrenType } from '@/core/types'

// Component Imports
import LayoutContent from '@/components/layout/vertical/LayoutContent'

// Util Imports
import { verticalLayoutClasses } from '@/utils/layoutClasses'

type CourseLayoutProps = ChildrenType & {
  navbar?: ReactNode
  footer?: ReactNode
}

const CourseLayout = (props: CourseLayoutProps) => {
  // Props
  const { navbar, children } = props

  return (
    <div className={classnames(verticalLayoutClasses.root, 'flex flex-auto')}>
      <div className={classnames(verticalLayoutClasses.contentWrapper, 'flex flex-col min-is-0 is-full')}>
        {navbar || null}
        <div className='flex flex-auto'>
          <LayoutContent>{children}</LayoutContent>
        </div>
      </div>
    </div>
  )
}

export default CourseLayout
