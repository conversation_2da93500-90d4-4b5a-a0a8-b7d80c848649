'use client'
import { useRouter } from 'next/navigation'

// MUI Imports
import { Card, Typography } from '@mui/material'

const Navbar = () => {
  const router = useRouter()

  const onBack = () => {
    const id = localStorage.getItem('courseId')

    router.push(`/course-detail/${id}`)
  }

  return (
    <Card className='flex items-center gap-4 is-full shadow-xs sticky top-0 z-20 px-6 py-4 rounded-none'>
      <div className='flex items-center text-primary gap-1 cursor-pointer' onClick={onBack}>
        <i className='ri-arrow-left-s-line'></i>
        <Typography color='primary' className='hover:underline text-primary'>
          Back to Practice test
        </Typography>
      </div>
    </Card>
  )
}

export default Navbar
