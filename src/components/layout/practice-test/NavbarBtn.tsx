'use client'
import { useRouter } from 'next/navigation'

// MUI Imports
import { Box, Button, Card, Typography } from '@mui/material'

interface Props {
  name: string
  handleSaveDraft?: (id: string) => void
  handlePublish?: (id: string) => void
  disableBtn?: boolean
}

const Navbar: React.FC<Props> = ({ name, handleSaveDraft, handlePublish, disableBtn = false }) => {
  const router = useRouter()

  const onBack = () => {
    const id = localStorage.getItem('courseId')

    router.push(`/course-detail/${id}`)
  }

  return (
    <Card className='flex items-center gap-4 is-full shadow-xs sticky top-0 z-20 px-6 py-4 rounded-none'>
      <Box className='flex items-center justify-between w-full'>
        <Box className='flex items-center gap-3'>
          <div className='flex items-center text-primary gap-1 cursor-pointer' onClick={onBack}>
            <i className='ri-arrow-left-s-line'></i>
            <Typography color='primary' className='hover:underline text-primary'>
              Back to Practice test
            </Typography>
          </div>
          <Typography color='text.primary'>{name}</Typography>
        </Box>
        <Box className='flex items-center gap-3'>
          <Button variant='outlined' color='secondary' onClick={() => handleSaveDraft && handleSaveDraft(name)}>
            Save draft
          </Button>
          <Button
            variant='contained'
            color='primary'
            onClick={() => handlePublish && handlePublish(name)}
            disabled={disableBtn}
          >
            Publish
          </Button>
        </Box>
      </Box>
    </Card>
  )
}

export default Navbar
