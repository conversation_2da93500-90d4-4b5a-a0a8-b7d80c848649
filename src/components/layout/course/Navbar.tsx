'use client'
import { useRouter } from 'next/navigation'

// MUI Imports
import { Card, Typography } from '@mui/material'

const Navbar = ({
  name,
  contentBack,
  linkBack = '/courses'
}: {
  name: string
  contentBack: string
  linkBack?: string
}) => {
  const router = useRouter()

  const onBack = () => {
    localStorage.setItem('activeTab', 'landingPage')

    router.push(linkBack)
  }

  return (
    <Card className='flex items-center gap-4 is-full shadow-xs sticky top-0 z-20 px-6 py-4 rounded-none'>
      <div className='flex items-center text-primary gap-1 cursor-pointer' onClick={onBack}>
        <i className='ri-arrow-left-s-line'></i>
        <Typography color='primary' className='hover:underline text-primary'>
          {contentBack}
        </Typography>
      </div>
      <Typography color='textPrimary' variant='h6'>
        {name}
      </Typography>
    </Card>
  )
}

export default Navbar
