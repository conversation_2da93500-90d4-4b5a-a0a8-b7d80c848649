// Third-party Imports
import classnames from 'classnames'

// Component Imports
import ModeDropdown from '@/components/layout/shared/ModeDropdown'
import UserDropdown from '@/components/layout/shared/UserDropdown'

// Util Imports
import { verticalLayoutClasses } from '@/utils/layoutClasses'
import type { IUser } from '@/types'

const NavbarContent: React.FC<IUser> = ({ avatar, name, email }) => {
  return (
    <div className={classnames(verticalLayoutClasses.navbarContent, 'flex items-center justify-end gap-4 is-full')}>
      <ModeDropdown />
      <UserDropdown name={name} email={email} avatar={avatar} />
    </div>
  )
}

export default NavbarContent
