'use client'

// Third-party Imports
import classnames from 'classnames'
import type { CSSObject } from '@emotion/styled'

// Type Imports

// Util Imports
import { verticalLayoutClasses } from '@/utils/layoutClasses'

// Styled Component Imports
import StyledHeader from '@/styles/vertical/StyledHeader'

import type { ChildrenType } from '@/core/types'

type Props = ChildrenType & {
  overrideStyles?: CSSObject
}

const LayoutNavbar = (props: Props) => {
  // Props
  const { children, overrideStyles } = props

  return (
    <StyledHeader
      overrideStyles={overrideStyles}
      className={classnames(
        verticalLayoutClasses.header,
        verticalLayoutClasses.headerContentCompact,
        verticalLayoutClasses.headerStatic,
        verticalLayoutClasses.headerDetached
      )}
    >
      <div className={classnames(verticalLayoutClasses.navbar, 'flex bs-full w-full max-w-none')}>{children}</div>
    </StyledHeader>
  )
}

export default LayoutNavbar
