// Component Imports
import NavbarContent from '@/components/layout/vertical/NavbarContent'
import LayoutNavbar from '@/components/layout/vertical/LayoutNavbar'

// Type Imports
import type { IUser } from '@/types'

const Navbar: React.FC<IUser> = ({ avatar, name, email }) => {
  return (
    <LayoutNavbar>
      <NavbarContent avatar={avatar} name={name} email={email} />
    </LayoutNavbar>
  )
}

export default Navbar
