'use client'

// Third-party Imports
import classnames from 'classnames'

// Type Imports
import type { ChildrenType } from '@/core/types'

// Util Imports
import { verticalLayoutClasses } from '@/utils/layoutClasses'

// Styled Component Imports
import StyledMain from '@/styles/shared/StyledMain'

const LayoutContent = ({ children }: ChildrenType) => {
  return (
    <StyledMain
      isContentCompact={true}
      className={classnames(
        verticalLayoutClasses.content,
        verticalLayoutClasses.contentCompact,
        'flex-auto is-full max-w-full'
      )}
    >
      {children}
    </StyledMain>
  )
}

export default LayoutContent
