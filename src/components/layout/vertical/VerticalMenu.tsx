// MUI Imports
import { useTheme } from '@mui/material/styles'

// Third-party Imports
import PerfectScrollbar from 'react-perfect-scrollbar'

// Component Imports
import { Menu, MenuItem, MenuSection } from '@/menu/vertical-menu'

// Hook Imports
import useVerticalNav from '@/menu/hooks/useVerticalNav'

// Style Imports
import menuItemStyles from '@/core/styles/vertical/menuItemStyles'
import menuSectionStyles from '@/core/styles/vertical/menuSectionStyles'
import UserIcon from '@/components/Icons/GranduationUser'
import InstructorIcon from '@/components/Icons/Instructor'
import MoneyIcon from '@/components/Icons/Money'

const VerticalMenu = ({ scrollMenu }: { scrollMenu: (container: any, isPerfectScrollbar: boolean) => void }) => {
  // Hooks
  const theme = useTheme()
  const { isBreakpointReached } = useVerticalNav()

  const ScrollWrapper = isBreakpointReached ? 'div' : PerfectScrollbar

  return (
    // eslint-disable-next-line lines-around-comment
    /* Custom scrollbar instead of browser scroll, remove if you want browser scroll only */
    <ScrollWrapper
      {...(isBreakpointReached
        ? {
            className: 'bs-full overflow-y-auto overflow-x-hidden',
            onScroll: container => scrollMenu(container, false)
          }
        : {
            options: { wheelPropagation: false, suppressScrollX: true },
            onScrollY: container => scrollMenu(container, true)
          })}
    >
      <Menu
        menuItemStyles={menuItemStyles(theme)}
        renderExpandedMenuItemIcon={{ icon: <i className='ri-circle-line' /> }}
        menuSectionStyles={menuSectionStyles(theme)}
      >
        <MenuItem href='/' icon={<i className='ri-home-smile-line' />}>
          Dashboards
        </MenuItem>
        <MenuSection label='MANAGEMENT'>
          <MenuItem href='/courses' icon={<i className='ri-file-text-line' />}>
            Courses
          </MenuItem>
          <MenuItem href='/user' icon={<UserIcon />}>
            User
          </MenuItem>
          <MenuItem href='/instructor' icon={<InstructorIcon />}>
            Instructors
          </MenuItem>
          <MenuItem href='/rating' icon={<i className='ri-star-fill' />}>
            Rating
          </MenuItem>
          <MenuItem href='/class-room' icon={<i className='ri-graduation-cap-fill' />}>
            Class Room
          </MenuItem>
        </MenuSection>
        <MenuSection label='REVENUE'>
          <MenuItem href='/order-tracking' icon={<i className='ri-shopping-cart-fill'></i>}>
            Order Tracking
          </MenuItem>
          <MenuItem href='/transaction-tracking' icon={<MoneyIcon />}>
            Transaction Tracking
          </MenuItem>
        </MenuSection>
      </Menu>
    </ScrollWrapper>
  )
}

export default VerticalMenu
