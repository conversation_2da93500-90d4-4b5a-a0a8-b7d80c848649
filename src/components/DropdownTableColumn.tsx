'use client'
import React from 'react'

// MUI Imports
import { Checkbox, Menu, MenuItem, IconButton } from '@mui/material'

interface DropdownProps {
  columns: { id: string; label: string; fixed: boolean }[]
  selectedColumns: string[]
  onToggleColumn: (columnId: string) => void
}

const Dropdown: React.FC<DropdownProps> = ({ columns, selectedColumns, onToggleColumn }) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)

  const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleCloseMenu = () => {
    setAnchorEl(null)
  }

  return (
    <div>
      <IconButton
        onClick={handleOpenMenu}
        className='px-3 py-[6px] rounded border border-solid border-gray-300 text-base font-semibold text-gray-600 bg-white'
      >
        <i className='ri-equalizer-2-line mr-[6px]'></i> View
      </IconButton>
      <Menu anchorEl={anchorEl} open={open} onClose={handleCloseMenu}>
        {columns.map(col => (
          <MenuItem key={col.id} onClick={() => onToggleColumn(col.id)} disabled={col.fixed}>
            <Checkbox checked={selectedColumns.includes(col.id)} disabled={col.fixed} />
            {col.label}
          </MenuItem>
        ))}
      </Menu>
    </div>
  )
}

export default Dropdown
