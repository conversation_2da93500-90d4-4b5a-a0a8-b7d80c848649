import React from 'react'

import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TableSortLabel,
  CircularProgress
} from '@mui/material'

import type { SortConfig, TableColumn } from '@/types'

interface TableProps {
  data: any[]
  columns: TableColumn[]
  selectedColumns: string[]
  page: number
  rowsPerPage: number
  sortConfig?: SortConfig
  handleChangePage: (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => void
  handleChangeRowsPerPage: (event: React.ChangeEvent<HTMLInputElement>) => void
  renderCell?: (columnId: string, rowData: any) => React.ReactNode
  actions?: React.ReactNode | ((row: any) => React.ReactNode)
  actionType?: 'static' | 'dynamic'
  onActionClick?: (row: any) => void
  className?: string
  headerClassName?: string
  rowClassName?: string
  cellClassName?: string
  handleSort?: (columnId: string, direction: 'asc' | 'desc') => void
  loading: boolean
  total: number
  messageDataFound: string
}

const TableCustom: React.FC<TableProps> = ({
  data,
  columns,
  selectedColumns,
  page,
  rowsPerPage,
  sortConfig,
  handleSort,
  handleChangePage,
  handleChangeRowsPerPage,
  renderCell,
  actions,
  actionType = 'static',
  onActionClick,
  className = '',
  headerClassName = '',
  rowClassName = '',
  cellClassName = '',
  loading,
  total,
  messageDataFound
}) => {
  const renderSortLabel = (column: TableColumn) => {
    if (!column.sortable) return column.label

    return (
      <TableSortLabel
        active={sortConfig?.key === column.id}
        direction={sortConfig?.key === column.id ? sortConfig.direction : 'asc'}
        onClick={() => handleSort && handleSort(column.id, sortConfig?.direction === 'asc' ? 'desc' : 'asc')}
        sx={{
          '& .MuiTableSortLabel-icon': {
            opacity: 1
          }
        }}
      >
        {column.label}
      </TableSortLabel>
    )
  }

  const renderActions = (row: any): React.ReactNode => {
    if (actionType === 'dynamic' && typeof actions === 'function') {
      return actions(row)
    }

    if (onActionClick) {
      return (
        <div onClick={() => onActionClick(row)} className='cursor-pointer'>
          {actions as React.ReactNode}
        </div>
      )
    }

    return actions as React.ReactNode
  }

  return (
    <>
      <TableContainer className={`border border-solid rounded ${className}`}>
        <Table className='w-full table-auto'>
          <TableHead>
            <TableRow className={headerClassName}>
              {columns.map(
                col =>
                  selectedColumns.includes(col.id) && (
                    <TableCell
                      key={col.id}
                      width={col.width}
                      className={`px-4 py-2 border-b text-base whitespace-nowrap ${cellClassName}`}
                    >
                      {renderSortLabel(col)}
                    </TableCell>
                  )
              )}
              {actions && <TableCell className='px-4 py-2 border-b'></TableCell>}
            </TableRow>
          </TableHead>

          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell
                  colSpan={actions ? selectedColumns.length + 1 : selectedColumns.length}
                  className='text-center py-4 text-sm'
                >
                  <CircularProgress color='primary' />
                </TableCell>
              </TableRow>
            ) : data.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={actions ? selectedColumns.length + 1 : selectedColumns.length}
                  className='text-center py-4 text-sm'
                >
                  {messageDataFound}
                </TableCell>
              </TableRow>
            ) : (
              data.map(row => (
                <TableRow key={row.courseId} className={`capitalize ${rowClassName}`}>
                  {columns.map(
                    col =>
                      selectedColumns.includes(col.id) && (
                        <TableCell
                          width={col.width}
                          className={`px-4 py-2 border-b text-base ${cellClassName}`}
                          key={col.id}
                        >
                          {renderCell ? renderCell(col.id, row) : row[col.id]}
                        </TableCell>
                      )
                  )}
                  {actions && <TableCell className='cursor-pointer'>{renderActions(row)}</TableCell>}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[10, 25, 50]}
        component='div'
        count={total}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </>
  )
}

export default TableCustom
