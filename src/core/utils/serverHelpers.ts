// Next Imports
import type { Settings } from '@/core/contexts/settingsContext'
import type { SystemMode } from '@/core/types'
import themeConfig from '@/configs/themeConfig'

function getCookie(name: string) {
  const nameEQ = name + '='

  if (typeof document === 'undefined') return null
  const ca = document.cookie.split(';')

  for (let i = 0; i < ca.length; i++) {
    let c = ca[i]

    while (c.charAt(0) == ' ') c = c.substring(1, c.length)

    if (c.indexOf(nameEQ) == 0) {
      const cookieValue = c.substring(nameEQ.length, c.length)

      try {
        // First decode the URL encoding, then handle any JSON encoding
        return decodeURIComponent(cookieValue)
      } catch (e) {
        console.error('Error decoding cookie:', e)

        return cookieValue
      }
    }
  }

  return null
}

export const getSettingsFromCookie = (): Settings => {
  const cookieName = themeConfig.settingsCookieName
  const cookieValue = getCookie(cookieName)

  try {
    // Handle the case where the cookie might be already JSO<PERSON> parsed
    if (cookieValue === null) return {} as Settings

    if (typeof cookieValue === 'object') {
      return cookieValue as Settings
    }

    return JSON.parse(cookieValue)
  } catch (e) {
    console.error('Error parsing settings cookie:', e)

    return {} as Settings
  }
}

export const getMode = () => {
  const settingsCookie = getSettingsFromCookie()

  // Get mode from cookie or fallback to theme config
  return settingsCookie.mode || themeConfig.mode
}

export const getSystemMode = (): SystemMode => {
  return getMode()
}

export const getServerMode = () => {
  return getMode()
}

// Optional: Add a function to set cookies properly
export const setCookie = (name: string, value: string | object, days: number = 7) => {
  if (typeof document === 'undefined') return

  const expires = new Date()

  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000)

  // If value is an object, stringify it
  const cookieValue = typeof value === 'object' ? JSON.stringify(value) : value

  // Properly encode the cookie value
  const encodedValue = encodeURIComponent(cookieValue)

  document.cookie = `${name}=${encodedValue}; expires=${expires.toUTCString()}; path=/`
}
