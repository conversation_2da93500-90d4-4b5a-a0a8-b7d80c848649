// Override Imports
import accordion from '@/core/theme/overrides/accordion'
import Alerts from '@/core/theme/overrides/alerts'
import Autocomplete from '@/core/theme/overrides/autocomplete'
import avatar from '@/core/theme/overrides/avatar'
import backdrop from '@/core/theme/overrides/backdrop'
import badges from '@/core/theme/overrides/badges'
import breadcrumbs from '@/core/theme/overrides/breadcrumbs'
import button from '@/core/theme/overrides/button'
import buttonGroup from '@/core/theme/overrides/button-group'
import card from '@/core/theme/overrides/card'
import Checkbox from '@/core/theme/overrides/checkbox'
import chip from '@/core/theme/overrides/chip'
import dialog from '@/core/theme/overrides/dialog'
import drawer from '@/core/theme/overrides/drawer'
import fab from '@/core/theme/overrides/fab'
import formControlLabel from '@/core/theme/overrides/form-control-label'
import iconButton from '@/core/theme/overrides/icon-button'
import input from '@/core/theme/overrides/input'
import list from '@/core/theme/overrides/list'
import menu from '@/core/theme/overrides/menu'
import pagination from '@/core/theme/overrides/pagination'
import paper from '@/core/theme/overrides/paper'
import popover from '@/core/theme/overrides/popover'
import progress from '@/core/theme/overrides/progress'
import Radio from '@/core/theme/overrides/radio'
import Rating from '@/core/theme/overrides/rating'
import Select from '@/core/theme/overrides/select'
import slider from '@/core/theme/overrides/slider'
import snackbar from '@/core/theme/overrides/snackbar'
import switchOverrides from '@/core/theme/overrides/switch'
import tablePagination from '@/core/theme/overrides/table-pagination'
import tabs from '@/core/theme/overrides/tabs'
import timeline from '@/core/theme/overrides/timeline'
import toggleButton from '@/core/theme/overrides/toggle-button'
import tooltip from '@/core/theme/overrides/tooltip'
import typography from '@/core/theme/overrides/typography'

const overrides = () => {
  const skin = 'default'

  return Object.assign(
    {},
    accordion(skin),
    Alerts,
    Autocomplete(skin),
    avatar,
    backdrop,
    badges,
    breadcrumbs,
    button,
    buttonGroup,
    card(skin),
    Checkbox,
    chip,
    dialog(skin),
    drawer(skin),
    fab,
    formControlLabel,
    iconButton,
    input,
    list,
    menu(skin),
    pagination,
    paper,
    popover(skin),
    progress,
    Radio,
    Rating,
    Select,
    slider,
    snackbar(skin),
    switchOverrides,
    tablePagination,
    tabs,
    timeline,
    toggleButton,
    tooltip,
    typography
  )
}

export default overrides
