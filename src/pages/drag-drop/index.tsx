import React, { useState } from 'react'

import { Tab<PERSON>ontext, TabList, TabPanel } from '@mui/lab'
import { Box, Tab, Typography } from '@mui/material'

const DragDropTabsLab = () => {
  const [tabs, setTabs] = useState([
    { id: '1', label: 'Tab 1' },
    { id: '2', label: 'Tab 2' },
    { id: '3', label: 'Tab 3' }
  ])

  const [activeTab, setActiveTab] = useState('1')

  const handleDragStart = (e: React.DragEvent, index: number) => {
    e.dataTransfer.setData('text/plain', index.toString())
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    const dragIndex = parseInt(e.dataTransfer.getData('text/plain'), 10)

    if (dragIndex === dropIndex) return

    const updatedTabs = [...tabs]
    const draggedTab = updatedTabs[dragIndex]

    updatedTabs.splice(dragIndex, 1)
    updatedTabs.splice(dropIndex, 0, draggedTab)

    setTabs(updatedTabs)
  }

  return (
    <TabContext value={activeTab}>
      <Box sx={{ display: 'flex', flexDirection: 'row' }}>
        <TabList orientation='vertical' onChange={(e, value) => setActiveTab(value)}>
          {tabs.map((tab, index) => (
            <Tab
              key={tab.id}
              value={tab.id}
              label={
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    width: '100%'
                  }}
                  draggable
                  onDragStart={e => handleDragStart(e, index)}
                  onDragOver={handleDragOver}
                  onDrop={e => handleDrop(e, index)}
                >
                  <Typography variant='body1' sx={{ flexGrow: 1 }}>
                    {tab.label}
                  </Typography>
                  <i className='ri-drag-move-line' style={{ cursor: 'move', paddingLeft: '8px' }}></i>
                </Box>
              }
              sx={{ cursor: 'default' }}
            />
          ))}
        </TabList>
        <Box sx={{ flexGrow: 1, padding: 2 }}>
          {tabs.map(tab => (
            <TabPanel key={tab.id} value={tab.id}>
              Content for {tab.label}
            </TabPanel>
          ))}
        </Box>
      </Box>
    </TabContext>
  )
}

export default DragDropTabsLab
