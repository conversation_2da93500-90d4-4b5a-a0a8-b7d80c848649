import React, { useState } from 'react'

import {
  Card,
  CardContent,
  CardHeader,
  Button,
  TextField,
  Chip,
  Divider,
  Typography,
  Box,
  Grid,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material'

import Editor from '../../components/Editor'

const PageEditorDemo: React.FC = () => {
  const [content, setContent] = useState('<h1>Welcome to the Page Editor Demo</h1><p>Start writing your content here...</p>')
  const [title, setTitle] = useState('My New Page')
  const [slug, setSlug] = useState('my-new-page')
  const [description, setDescription] = useState('A demo page created with our editor component')
  const [tags, setTags] = useState<string[]>(['demo', 'editor', 'react'])
  const [newTag, setNewTag] = useState('')
  const [author, setAuthor] = useState('Demo User')
  const [previewMode, setPreviewMode] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [isPublished, setIsPublished] = useState(false)

  const handleContentChange = (value: string) => {
    setContent(value)
  }

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()])
      setNewTag('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleSave = () => {
    console.log('Saving page...', {
      title,
      slug,
      description,
      content,
      tags,
      author,
      isPublished,
      timestamp: new Date().toISOString()
    })
    alert('Page saved successfully!')
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleTitleChange = (newTitle: string) => {
    setTitle(newTitle)
    setSlug(generateSlug(newTitle))
  }

  return (
    <Box className="min-h-screen bg-gray-50 p-4">
      <Box className="max-w-6xl mx-auto">
        {/* Header */}
        <Box className="mb-6">
          <Box className="flex items-center justify-between mb-4">
            <Box>
              <Typography variant="h3" component="h1" className="text-gray-900 flex items-center gap-2 mb-2">
                <i className="ri-file-text-line text-blue-600" style={{ fontSize: '2rem' }} />
                Page Editor Demo
              </Typography>
              <Typography variant="body1" className="text-gray-600">
                Showcase of the Editor component with full page editing capabilities
              </Typography>
            </Box>
            <Box className="flex items-center gap-2">
              <Button
                variant={previewMode ? "contained" : "outlined"}
                onClick={() => setPreviewMode(!previewMode)}
                startIcon={<i className={previewMode ? 'ri-edit-line' : 'ri-eye-line'} />}
              >
                {previewMode ? 'Edit' : 'Preview'}
              </Button>
              <Button
                variant={showSettings ? "contained" : "outlined"}
                onClick={() => setShowSettings(!showSettings)}
                startIcon={<i className="ri-settings-line" />}
              >
                Settings
              </Button>
              <Button 
                variant="contained" 
                onClick={handleSave}
                startIcon={<i className="ri-save-line" />}
              >
                Save Page
              </Button>
            </Box>
          </Box>

          {/* Status Bar */}
          <Box className="flex items-center gap-4 text-sm text-gray-600">
            <Box className="flex items-center gap-1">
              <i className="ri-calendar-line" />
              <Typography variant="body2">Last saved: {new Date().toLocaleString()}</Typography>
            </Box>
            <Box className="flex items-center gap-1">
              <i className="ri-user-line" />
              <Typography variant="body2">Author: {author}</Typography>
            </Box>
            <Box className="flex items-center gap-1">
              <i className="ri-global-line" />
              <Typography variant="body2">Status: {isPublished ? 'Published' : 'Draft'}</Typography>
            </Box>
          </Box>
        </Box>

        <Grid container spacing={3}>
          {/* Main Editor Area */}
          <Grid item xs={12} lg={9}>
            <Card>
              <CardHeader 
                title="Page Content"
                className="pb-4"
              />
              
              <CardContent>
                <Box className="space-y-4 mb-6">
                  <Box>
                    <Typography variant="body2" className="text-gray-700 mb-2 font-medium">
                      Page Title
                    </Typography>
                    <TextField
                      fullWidth
                      value={title}
                      onChange={(e) => handleTitleChange(e.target.value)}
                      placeholder="Enter page title..."
                      variant="outlined"
                      size="medium"
                      sx={{ 
                        '& .MuiInputBase-input': { 
                          fontSize: '1.25rem',
                          fontWeight: 600 
                        }
                      }}
                    />
                  </Box>
                  
                  <Box>
                    <Typography variant="body2" className="text-gray-700 mb-2 font-medium">
                      URL Slug
                    </Typography>
                    <Box className="flex items-center gap-2">
                      <Typography variant="body2" className="text-gray-500">/pages/</Typography>
                      <TextField
                        fullWidth
                        value={slug}
                        onChange={(e) => setSlug(e.target.value)}
                        placeholder="page-url-slug"
                        variant="outlined"
                        size="medium"
                        sx={{
                          '& .MuiInputBase-input': {
                            fontFamily: 'monospace',
                            fontSize: '0.875rem'
                          }
                        }}
                      />
                    </Box>
                  </Box>

                  <Box>
                    <Typography variant="body2" className="text-gray-700 mb-2 font-medium">
                      Meta Description
                    </Typography>
                    <TextField
                      fullWidth
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      placeholder="Brief description for SEO..."
                      variant="outlined"
                      size="medium"
                    />
                  </Box>
                </Box>
              
                <Divider className="my-6" />
                
                {previewMode ? (
                  <Box>
                    <Typography variant="h4" className="mb-4">{title}</Typography>
                    <Box 
                      dangerouslySetInnerHTML={{ __html: content }}
                      className="min-h-[400px] p-4 border rounded-md bg-white prose max-w-none"
                      sx={{
                        '& h1, & h2, & h3, & h4, & h5, & h6': {
                          marginTop: '1.5rem',
                          marginBottom: '1rem'
                        },
                        '& p': {
                          marginBottom: '1rem'
                        },
                        '& ul, & ol': {
                          marginBottom: '1rem',
                          paddingLeft: '1.5rem'
                        }
                      }}
                    />
                  </Box>
                ) : (
                  <Box>
                    <Typography variant="body2" className="text-gray-700 mb-3 font-medium">
                      Page Content
                    </Typography>
                    <Editor
                      value={content}
                      onChange={handleContentChange}
                      placeholder="Start writing your page content..."
                      showToolbar={true}
                    />
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Sidebar */}
          <Grid item xs={12} lg={3}>
            <Box className="space-y-6">
              {/* Publish Settings */}
              <Card>
                <CardHeader 
                  title={
                    <Box className="flex items-center gap-2">
                      <i className="ri-global-line" />
                      <Typography variant="h6">Publish</Typography>
                    </Box>
                  }
                />
                <CardContent>
                  <Box className="space-y-4">
                    <Box className="flex items-center justify-between">
                      <Typography variant="body2" className="font-medium">Status:</Typography>
                      <Chip 
                        label={isPublished ? 'Published' : 'Draft'} 
                        color={isPublished ? "primary" : "default"}
                        size="small"
                      />
                    </Box>
                    
                    <Button 
                      variant={isPublished ? "outlined" : "contained"} 
                      fullWidth
                      onClick={() => setIsPublished(!isPublished)}
                    >
                      {isPublished ? 'Unpublish' : 'Publish'}
                    </Button>
                    
                    <Typography variant="caption" className="text-gray-500 block">
                      {isPublished 
                        ? 'This page is live and visible to everyone'
                        : 'This page is only visible to you'
                      }
                    </Typography>
                  </Box>
                </CardContent>
              </Card>

              {/* Tags */}
              <Card>
                <CardHeader 
                  title={
                    <Box className="flex items-center gap-2">
                      <i className="ri-price-tag-3-line" />
                      <Typography variant="h6">Tags</Typography>
                    </Box>
                  }
                />
                <CardContent>
                  <Box className="space-y-3">
                    <Box className="flex flex-wrap gap-2">
                      {tags.map((tag, index) => (
                        <Chip 
                          key={index} 
                          label={tag}
                          variant="outlined"
                          size="small"
                          onDelete={() => handleRemoveTag(tag)}
                          deleteIcon={<i className="ri-close-line" />}
                        />
                      ))}
                    </Box>
                    
                    <Box className="flex gap-2">
                      <TextField
                        size="small"
                        fullWidth
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        placeholder="Add tag..."
                        onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                      />
                      <IconButton size="small" onClick={handleAddTag}>
                        <i className="ri-add-line" />
                      </IconButton>
                    </Box>
                  </Box>
                </CardContent>
              </Card>

              {/* Page Settings */}
              {showSettings && (
                <Card>
                  <CardHeader 
                    title={
                      <Box className="flex items-center gap-2">
                        <i className="ri-settings-line" />
                        <Typography variant="h6">Settings</Typography>
                      </Box>
                    }
                  />
                  <CardContent>
                    <Box className="space-y-4">
                      <Box>
                        <Typography variant="body2" className="text-gray-700 mb-2 font-medium">
                          Author
                        </Typography>
                        <TextField
                          size="small"
                          fullWidth
                          value={author}
                          onChange={(e) => setAuthor(e.target.value)}
                          placeholder="Page author..."
                        />
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" className="text-gray-700 mb-2 font-medium">
                          Featured Image
                        </Typography>
                        <Button variant="outlined" fullWidth>
                          Upload Image
                        </Button>
                      </Box>
                      
                      <Box>
                        <Typography variant="body2" className="text-gray-700 mb-2 font-medium">
                          Template
                        </Typography>
                        <FormControl fullWidth size="small">
                          <Select defaultValue="default">
                            <MenuItem value="default">Default Page</MenuItem>
                            <MenuItem value="blog">Blog Post</MenuItem>
                            <MenuItem value="docs">Documentation</MenuItem>
                            <MenuItem value="landing">Landing Page</MenuItem>
                          </Select>
                        </FormControl>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              )}

              {/* Editor Info */}
              <Card>
                <CardHeader 
                  title={
                    <Typography variant="h6">Editor Features</Typography>
                  }
                />
                <CardContent>
                  <Box className="space-y-2">
                    {[
                      'Rich text formatting',
                      'Image upload to S3',
                      'Code syntax highlighting',
                      'Link management',
                      'Lists and tables',
                      'Responsive design'
                    ].map((feature, index) => (
                      <Box key={index} className="flex items-center gap-2">
                        <i className="ri-check-line text-green-600" />
                        <Typography variant="body2" className="text-gray-600">
                          {feature}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Box>
  )
}

export default PageEditorDemo