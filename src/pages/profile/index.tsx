import { type ReactElement } from 'react'

// MUI Imports
import { Divider, Typography } from '@mui/material'

// Components Imports
import Layout from '@/layouts/MainLayout'
import Profile from '@/views/profile'

const ProfileSetting = () => {
  return (
    <div>
      <Typography variant='h3' className='mb-2'>
        Profile
      </Typography>
      <Divider className='mb-4' />
      <Profile />
    </div>
  )
}

ProfileSetting.getLayout = function (page: ReactElement) {
  return <Layout>{page}</Layout>
}

export default ProfileSetting
