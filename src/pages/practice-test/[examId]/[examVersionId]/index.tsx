import React, { useState, useEffect, useRef } from 'react'

import { useRouter } from 'next/router'

import { <PERSON>b<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bPanel } from '@mui/lab'
import {
  Box,
  Button,
  CircularProgress,
  ClickAwayListener,
  Divider,
  Grid,
  Grow,
  IconButton,
  MenuItem,
  MenuList,
  Paper,
  Popper,
  Stack,
  Tab,
  TextField,
  Typography
} from '@mui/material'

import { FormProvider, useForm } from 'react-hook-form'

import * as yup from 'yup'

import { yupResolver } from '@hookform/resolvers/yup'

import Layout from '@/layouts/PracticeLayout'
import FormAdd from '@/views/practice-test/Form'
import CustomAvatar from '@/core/components/mui/Avatar'

import { getRandomId, stripHtml } from '@/utils'
import DialogCustom from '@/views/DialogConfirm'

import type { Answer, NewQuestion, Question } from '@/api/question'
import { useApiDelete, useApiGet, useApiPost, useApiPut } from '@/hooks/useApi'
import type { Exam } from '@/api/exam'
import { useToast } from '@/contexts/ToastContext'
import QuestionEmptyForm from '@/views/practice-test/QuestionEmptyForm'

type DialogType = {
  id: string
  dialogTitle: string
  dialogContent: string | React.ReactNode
  dialogActions: string | React.ReactNode
}

const transformQuestion = (question: Question): NewQuestion => {
  const answers: Answer[] = question?.answers.map((content, index) => ({
    id: index,
    content
  }))

  const correctAnswer: number[] = question?.correctAnswer
    .map(answer => answers.find(a => a.content === answer)?.id ?? -1)
    .filter(id => id !== -1)

  return {
    ...question,
    answers,
    correctAnswer
  }
}

const reverseTransformQuestion = (newQuestion: NewQuestion): Question => {
  const answers: string[] = newQuestion.answers.map(answer => answer.content)

  const correctAnswer: string[] = newQuestion.correctAnswer
    .map(id => newQuestion.answers.find(answer => answer.id === id)?.content ?? '')
    .filter(content => content !== '' || content !== null)

  return {
    ...newQuestion,
    answers,
    correctAnswer
  }
}

const getNewPos = (questions: Question[]): number => {
  if (questions.length === 0) return 1024

  return questions[questions.length - 1].pos + 1024
}

const schema = yup.object({
  description: yup.string().trim().required('Description is required')
})

const resolver = yupResolver(schema)

interface ActionType {
  type: string
  nextTab?: string
}

const PracticePlan = () => {
  const router = useRouter()
  const { examId, examVersionId } = router.query

  const initNewQuestion = {
    answers: [
      { id: getRandomId(), content: '' },
      { id: getRandomId(), content: '' }
    ],
    correctAnswer: [],
    questionId: 0,
    questionText: '',
    examVersionId: Number(examVersionId) || 0,
    explanation: '',
    pos: 0,
    createdAt: ''
  }

  const [tabActive, setTabActive] = useState<NewQuestion>(initNewQuestion)

  const { showToast } = useToast()

  const { data: examData, isLoading } = useApiGet<Exam>(`/exams/${examId}`, 'exam-detail' + examId)

  const { data: questionData, isLoading: isLoadingQuestion } = useApiGet<Question[]>(
    `/exam-versions/${examVersionId}/questions`,
    'question-detail' + examVersionId
  )

  const updateExam = useApiPut<Exam, Partial<Exam>>()

  const createQuestion = useApiPost<Question, Partial<Question>>()

  const updateQuestion = useApiPut<Question, Partial<Question>>()

  const publishCourse = useApiPut<any, { isPublished: boolean; description: string }>()

  const deleteQuestion = useApiDelete<{ id: string }>()

  const methodsPlan = useForm({ defaultValues: examData })

  const methodsNewQuestion = useForm({ defaultValues: tabActive })

  const {
    reset: resetPlan,
    formState: { isDirty: isDirtyPlan, isValid: isValidPlan },
    handleSubmit: handleSubmitPlan
  } = methodsPlan

  const {
    handleSubmit: handleSubmitNewQuestion,
    watch: watchNewQuestion,
    setError: setErrorNewQuestion,
    clearErrors,
    setValue: setQuestion,
    reset: resetQuestion,
    formState: { isDirty: isDirtyQuestion, isValid: isValidQuestion }
  } = methodsNewQuestion

  const {
    formState: { errors: errorPublish },
    reset: resetPublish,
    handleSubmit: handleSubmitPublish,
    clearErrors: clearErrorPublish,
    register: registerPublish
  } = useForm<{ description: string }>({
    resolver: resolver,
    defaultValues: {
      description: ''
    },
    mode: 'onBlur'
  })

  const [activeTab, setActiveTab] = useState<string>('-1')
  const [openDialog, setOpenDialog] = useState(false)
  const [tabNewQuestion] = useState<NewQuestion>(initNewQuestion)

  const handleSubmitPlanRef = useRef<(() => void) | null>(null)

  const handleSubmitNewQuestionRef = useRef<(() => void) | null>(null)

  const [title, setTitle] = useState<string>('')

  const [actionType, setActionType] = useState<ActionType | null>(null)

  const [open, setOpen] = useState(false)

  const [isVisible, setIsVisible] = useState(false)

  const [isDisable, setIsDisable] = useState<boolean>(false)

  const [dialogActive, setDialogActive] = useState<DialogType>({
    id: '',
    dialogTitle: '',
    dialogContent: '',
    dialogActions: ''
  })

  const [typeAdd, setTypeAdd] = useState<string | null>(null)
  const anchorRef = useRef<HTMLButtonElement>(null)
  const actionTypeRef = useRef(actionType)

  // Function to complete the tab change
  const completeTabChange = (question: Question, type: string) => {
    type === 'add' && setActiveTab(question?.questionId?.toString())

    setTabActive(transformQuestion(question))
  }

  const handleSaveAndChangTab = async () => {
    if (activeTab === '-1') {
      await handleSubmitPlanRef.current?.()

      if (!isValidPlan) {
        setOpenDialog(false)

        return
      }

      setActiveTab('0')
      setTabActive(initNewQuestion)
      setOpenDialog(false)

      return
    } else {
      await handleSubmitNewQuestionRef.current?.()

      if (!isValidQuestion) {
        setOpenDialog(false)

        return
      }

      if (activeTab === '0') {
        setActiveTab('0')
        setTabActive(initNewQuestion)
        setOpenDialog(false)

        return
      } else {
        ;(questionData?.find(question => question.questionId.toString() === actionTypeRef.current?.nextTab)
          ?.correctAnswer.length || 0) <= 1
          ? setTypeAdd('multipleChoice')
          : setTypeAdd('multipleSelection')
        setActiveTab(actionTypeRef.current?.nextTab || '0')
        setTitle(questionData?.find(q => q.questionId === Number(actionTypeRef.current?.nextTab))?.questionText || '')

        await setTabActive(
          transformQuestion(
            questionData?.find(q => q.questionId === Number(actionTypeRef.current?.nextTab)) as Question
          )
        )

        setOpenDialog(false)

        return
      }
    }
  }

  // Also update handleAddQuestion
  const handleAddQuestion = (type: string) => {
    setOpen(false)
    setTypeAdd(type)

    if ((activeTab === '-1' && isDirtyPlan) || (activeTab !== '0' && isDirtyQuestion)) {
      setDialogActive(dialogList.find(item => item.id === 'saveChange')!)
      setOpenDialog(true)
    } else if (activeTab !== '0' && (!isDirtyPlan || !isDirtyQuestion)) {
      setActiveTab('0')
      setTabActive(initNewQuestion)
      setTitle(initNewQuestion.questionText)
    } else if ((activeTab as string) === '0') {
      setDialogActive(dialogList.find(item => item.id === 'saveChange')!)
      setOpenDialog(true)
      setActionType({ type: 'add' })
    }
  }

  // Handle dialog responses
  const handleDiscardChanges = () => {
    if (actionTypeRef.current?.type === 'add') {
      if (activeTab === '0') {
        setActiveTab('0')
        setTabActive(initNewQuestion)
        setTitle(initNewQuestion.questionText)
        setOpenDialog(false)
        setActionType(null)
      } else {
        setActiveTab('0')
        setTabActive(initNewQuestion)
        setTitle(initNewQuestion.questionText)
        setOpenDialog(false)
        setActionType(null)
      }
    } else {
      ;(questionData?.find(question => question.questionId.toString() === actionTypeRef.current?.nextTab)?.correctAnswer
        .length || 0) <= 1
        ? setTypeAdd('multipleChoice')
        : setTypeAdd('multipleSelection')
      setActiveTab(actionTypeRef.current?.nextTab || '0')
      setTitle(questionData?.find(q => q.questionId === Number(actionTypeRef.current?.nextTab))?.questionText || '')

      setTabActive(
        transformQuestion(questionData?.find(q => q.questionId === Number(actionTypeRef.current?.nextTab)) as Question)
      )
      setOpenDialog(false)
      setActionType(null)
    }
  }

  const [draggedItem, setDraggedItem] = useState<Question | null>(null)
  const [dragOverItemId, setDragOverItemId] = useState<string | number | null>(null)

  const POS_INCREMENT = 1024

  const rebalancePositions = (questions: Array<any>): Array<any> => {
    if (!questions || questions.length === 0) return []

    const sortedQuestions = [...questions].sort((a, b) => a.pos - b.pos)

    return sortedQuestions.map((question, index) => ({
      ...question,
      pos: (index + 1) * POS_INCREMENT
    }))
  }

  const handleDragStart = (e: React.DragEvent, index: number) => {
    e.dataTransfer.setData('text/plain', index.toString())

    if (questionData) {
      setDraggedItem(questionData[index])
    }
  }

  const handleDragOver = (e: React.DragEvent, tabId: string | number) => {
    e.preventDefault()
    setDragOverItemId(tabId)
  }

  const handleDrop = async (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()
    const dragIndex = Number(e.dataTransfer.getData('text/plain'))

    if (!questionData || !draggedItem || dragIndex === dropIndex || dragIndex < 0 || dragIndex >= questionData.length) {
      return
    }

    const sortedQuestions = [...questionData].sort((a, b) => a.pos - b.pos)

    const sortedDragItem = sortedQuestions.find(q => q.questionId === draggedItem.questionId)

    if (!sortedDragItem) return

    const targetUIItem = questionData[dropIndex]

    const targetSortedIndex = sortedQuestions.findIndex(q => q.questionId === targetUIItem.questionId)

    let newPos: number

    if (dropIndex >= questionData.length - 1) {
      const lastItem = sortedQuestions[sortedQuestions.length - 1]

      newPos = lastItem.pos + POS_INCREMENT
    } else if (dropIndex === 0) {
      const firstItem = sortedQuestions[0]

      newPos = firstItem.pos / 2
    } else {
      if (dragIndex < dropIndex) {
        const targetPosIndex = sortedQuestions.findIndex(q => q.questionId === targetUIItem.questionId)
        const nextItem = sortedQuestions[targetPosIndex + 1] || sortedQuestions[targetPosIndex]

        newPos = (targetUIItem.pos + nextItem.pos) / 2
      } else {
        const prevItemIndex = Math.max(0, targetSortedIndex - 1)
        const prevItem = sortedQuestions[prevItemIndex]

        newPos = (prevItem.pos + targetUIItem.pos) / 2
      }
    }

    const minDistance = 0.001

    if (Math.abs(newPos - sortedDragItem.pos) < minDistance) {
      const rebalancedQuestions = rebalancePositions(questionData)

      for (const q of rebalancedQuestions) {
        if (q.questionId === draggedItem.questionId) {
          continue
        }

        await updateQuestion.mutate(
          { endpoint: `/questions/${q.questionId}`, data: { pos: q.pos } },
          {
            onSuccess: () => {},
            onError: (error: any) => {
              showToast(`Error updating question ${q.questionId}: ${error.response?.data?.message}`, 'error')
            }
          }
        )
      }

      const rebalancedDragItem = rebalancedQuestions.find(q => q.questionId === draggedItem.questionId)

      if (!rebalancedDragItem) return

      newPos = rebalancedDragItem.pos
    }

    await updateQuestion.mutate(
      { endpoint: `/questions/${draggedItem.questionId}`, data: { pos: newPos } },
      {
        onSuccess: () => {
          showToast('Question updated successfully', 'success')
        },
        onError: (error: any) => {
          showToast(error.response?.data?.message as string, 'error')
        }
      }
    )
  }

  const handleDragEnd = () => {
    setDraggedItem(null)
    setDragOverItemId(null)
  }

  // Handle toggle for the Popper
  const handleToggle = () => setOpen(prevOpen => !prevOpen)

  const handleClose = (event: Event | React.SyntheticEvent) => {
    if (anchorRef.current && anchorRef.current.contains(event.target as HTMLElement)) return
    setOpen(false)
  }

  const handleListKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Tab' || event.key === 'Escape') {
      event.preventDefault()
      setOpen(false)
    }
  }

  const onDeleteQuestion = () => {
    if (activeTab === '0') {
      const index = questionData?.findIndex(question => question.questionId.toString() === '0') || 0

      if (index !== -1) {
        questionData?.splice(index, 1)
      }

      if (questionData?.length === 0) {
        setActiveTab('-1')

        return
      }

      if (questionData?.length !== 0 && activeTab === questionData?.[0].questionId?.toString()) {
        setActiveTab(questionData && questionData?.length > 0 ? questionData[1].questionId.toString() : '-1')
        setTabActive(questionData && questionData?.length > 0 ? transformQuestion(questionData[1]) : initNewQuestion)
        setTitle(questionData[1]?.questionText || '')
      }

      if (activeTab !== questionData?.[0].questionId?.toString()) {
        setActiveTab(questionData && questionData?.length > 0 ? questionData[0].questionId.toString() : '-1')

        setTabActive(questionData && questionData?.length > 0 ? transformQuestion(questionData[0]) : initNewQuestion)

        setTitle(questionData && questionData?.length > 0 ? questionData[0]?.questionText : '')
      }
    } else {
      setDialogActive(dialogList.find(item => item.id === 'delete')!)
      setOpenDialog(true)
    }
  }

  const handleDeleteQuestion = () => {
    deleteQuestion.mutate(
      {
        endpoint: `/questions/${activeTab}`
      },
      {
        onSuccess: () => {
          showToast('Question deleted successfully', 'success')

          if (questionData?.length === 0) {
            setActiveTab('-1')
            setOpenDialog(false)

            return
          }

          if (activeTab === questionData?.[0].questionId?.toString()) {
            setActiveTab(questionData && questionData?.length > 1 ? questionData[1].questionId.toString() : '-1')
            setTabActive(
              questionData && questionData?.length > 1 ? transformQuestion(questionData[1]) : initNewQuestion
            )
            setTitle(questionData[1]?.questionText || '')
            setOpenDialog(false)

            return
          }

          if (activeTab !== questionData?.[0].questionId?.toString()) {
            setActiveTab(questionData && questionData?.length > 0 ? questionData[0].questionId.toString() : '-1')

            setTabActive(
              questionData && questionData?.length > 1 ? transformQuestion(questionData[0]) : initNewQuestion
            )

            setTitle(questionData && questionData?.length > 1 ? questionData[0]?.questionText : '')
            setOpenDialog(false)

            return
          }
        },
        onError: (error: any) => {
          showToast(error.response?.data?.message as string, 'error')
        }
      }
    )
  }

  const onSubmitPlan = async (data: Exam) => {
    await updateExam.mutate(
      {
        endpoint: `exams/${data.examId}`,
        data: {
          examName: data.examName,
          durationMinutes: Number(data.durationMinutes),
          passThreshold: Number(data.passThreshold),
          description: data.description,
          isRandomQuestion: data.isRandomQuestion
        }
      },
      {
        onSuccess: () => {
          showToast('Exam updated successfully', 'success')
          resetPlan(data)
        },
        onError: (error: any) => {
          showToast(error.response?.data?.message as string, 'error')
        }
      }
    )
  }

  const renderTitle = (tab: Question | NewQuestion): string => {
    const isEmptyTitle = (title: string) => title === '' || title === '<p><br></p>'
    const isCurrentTab = activeTab === tab.questionId.toString()

    if (tab.examVersionId === 0) {
      if (isEmptyTitle(title)) {
        return 'New Question'
      } else {
        return stripHtml(title || '')
      }
    }

    if (isCurrentTab && isEmptyTitle(tabActive?.questionText)) {
      return 'New Question'
    }

    if (!isCurrentTab && isEmptyTitle(tab.questionText)) {
      return 'New Question'
    }

    if (isCurrentTab) {
      return stripHtml(title || '')
    }

    return stripHtml(tab.questionText)
  }

  const handlePublishExamVersion = async (data: { description: string }) => {
    await publishCourse.mutate(
      {
        endpoint: `/exam-versions/${examVersionId}`,
        data: {
          isPublished: true,
          description: data.description
        }
      },
      {
        onSuccess: () => {
          showToast('Course published successfully', 'success')
          setIsDisable(true)
        },
        onError: (error: any) => {
          showToast(error.response?.data?.message as string, 'error')
        }
      }
    )
    resetPublish({ description: '' })
    setOpenDialog(false)
  }

  const dialogList = [
    {
      id: 'saveChange',
      dialogTitle: 'Updates are not saved',
      dialogContent: <Typography>You have unsaved changes on this page.</Typography>,
      dialogActions: (
        <>
          <Button onClick={handleDiscardChanges} color='primary'>
            Leave without saving
          </Button>
          <Button onClick={handleSaveAndChangTab} color='primary' variant='contained'>
            Save and continue
          </Button>
        </>
      )
    },
    {
      id: 'delete',
      dialogTitle: 'Delete Question',
      dialogContent: <Typography>Are you sure you want to delete this question?</Typography>,
      dialogActions: (
        <>
          <Button onClick={() => setOpenDialog(false)} color='secondary' variant='outlined'>
            Cancel
          </Button>
          <Button onClick={handleDeleteQuestion} color='primary' variant='contained'>
            Delete
          </Button>
        </>
      )
    }
  ]

  const handleChange = (value: string) => {
    setActionType({ type: 'change', nextTab: value })

    if (
      (activeTab === '-1' && isDirtyPlan) ||
      activeTab === '0' ||
      (activeTab !== '0' && activeTab !== '-1' && isDirtyQuestion)
    ) {
      setDialogActive(dialogList.find(item => item.id === 'saveChange')!)
      setOpenDialog(true)
    } else if (activeTab === '-1' && !isDirtyPlan) {
      setActiveTab(value)
      const newQuestion = questionData?.find(question => question.questionId.toString() === value)

      setTabActive(newQuestion ? transformQuestion(newQuestion) : initNewQuestion)

      setTitle(questionData?.find(question => question.questionId.toString() === value)?.questionText || '')

      if (value !== '0') {
        ;(questionData?.find(question => question.questionId.toString() === value)?.correctAnswer.length || 0) <= 1
          ? setTypeAdd('multipleChoice')
          : setTypeAdd('multipleSelection')
      }

      setActionType(null)
    } else if (value === '-1') {
      setActiveTab('-1')
      setTabActive(initNewQuestion)
      setActionType(null)
    } else if (value !== '-1' && value !== '0') {
      setActiveTab(value)
      const newQuestion = questionData?.find(question => question.questionId.toString() === value)

      setTabActive(newQuestion ? transformQuestion(newQuestion) : initNewQuestion)

      setTitle(questionData?.find(question => question.questionId.toString() === value)?.questionText || '')
      ;(questionData?.find(question => question.questionId.toString() === value)?.correctAnswer.length || 0) <= 1
        ? setTypeAdd('multipleChoice')
        : setTypeAdd('multipleSelection')

      setActionType(null)
    }
  }

  const handlePublish = () => {
    setIsVisible(true)
  }

  const onSubmitNewQuestion = async (data: NewQuestion) => {
    if (watchNewQuestion('correctAnswer').length === 0) {
      setErrorNewQuestion('correctAnswer', { message: 'Please select at least one correct answer' })

      return
    } else {
      clearErrors('correctAnswer')
    }

    const filteredAnswers = (data.answers || []).filter(answer => answer && answer.content)

    const sanitizedData = { ...data, answers: filteredAnswers }

    const question = reverseTransformQuestion(sanitizedData)

    if (data.questionId === 0) {
      await createQuestion.mutate(
        {
          endpoint: `/exam-versions/${data.examVersionId}/questions`,
          data: { ...question, pos: getNewPos(questionData || []) }
        },
        {
          onSuccess: (res: any) => {
            showToast('Question added', 'success')

            if (actionTypeRef.current?.type === 'add') {
              setActiveTab('0')
              setTabActive(initNewQuestion)
              setOpenDialog(false)
            } else {
              completeTabChange(res.data, 'add')
              resetQuestion()
            }
          },
          onError: (error: any) => {
            showToast(error.response?.data?.message as string, 'error')
          }
        }
      )

      return
    } else {
      await updateQuestion.mutate(
        {
          endpoint: `/questions/${data.questionId}`,
          data: question
        },
        {
          onSuccess: (res: any) => {
            showToast('Question updated successfully', 'success')

            if (actionTypeRef.current?.type !== 'change') {
              completeTabChange(res.data, 'add')
              resetQuestion()
            }
          },
          onError: (error: any) => {
            showToast(error.response?.data?.message as string, 'error')
          }
        }
      )
    }
  }

  useEffect(() => {
    actionTypeRef.current = actionType
  }, [actionType])

  useEffect(() => {
    handleSubmitPlanRef.current = handleSubmitPlan(onSubmitPlan)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [handleSubmitPlan])

  useEffect(() => {
    handleSubmitNewQuestionRef.current = handleSubmitNewQuestion(onSubmitNewQuestion)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [handleSubmitNewQuestion])

  useEffect(() => {
    if (examData) {
      resetPlan(examData)
    }
  }, [examData, resetPlan])

  useEffect(() => {
    if (tabActive) {
      setQuestion('answers', tabActive.answers)
      resetQuestion(tabActive)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tabActive])

  // useEffect(() => {
  //   console.log('âs')

  //   if (!isValidPublish) {
  //     setErrorPublish('description', { message: 'Description is required' })
  //   }
  // }, [isValidPublish])

  return (
    <Layout
      name={`Practice Test #${examVersionId} - ${examData?.examName}`}
      handlePublish={handlePublish}
      disableBtn={isDisable}
    >
      {isLoading && isLoadingQuestion ? (
        <div className='w-full flex justify-center items-center'>
          <CircularProgress />
        </div>
      ) : (
        <TabContext value={activeTab}>
          <Grid container direction='row' spacing={6}>
            <Grid item xs={3} className='pl-0 max-h-[calc(100vh-94px)] overflow-y-auto'>
              <TabList
                orientation='vertical'
                onChange={(e, value) => handleChange(value)}
                className='border-none min-h-56'
              >
                <Tab
                  label='Plan practice test'
                  iconPosition='start'
                  value='-1'
                  className='flex justify-start items-start !min-w-full'
                />
                <Divider className='py-2 ml-4' />
                <Box className='pl-4 pt-2 flex justify-between items-center'>
                  <Typography variant='h6' color='text.primary' className='text-base'>
                    Question
                  </Typography>
                  <Stack direction='row' spacing={2}>
                    <IconButton
                      className='pr-0 pb-3'
                      ref={anchorRef}
                      id='composition-button'
                      aria-controls={open ? 'composition-menu' : undefined}
                      aria-expanded={open ? 'true' : undefined}
                      aria-haspopup='true'
                      onClick={handleToggle}
                    >
                      <CustomAvatar color='primary' className='w-8 h-8'>
                        <i className='ri-add-line'></i>
                      </CustomAvatar>
                    </IconButton>
                    <Popper
                      open={open}
                      anchorEl={anchorRef.current}
                      placement='bottom-end'
                      transition
                      disablePortal
                      className='z-10 !mt-1'
                    >
                      {({ TransitionProps, placement }) => (
                        <Grow
                          {...TransitionProps}
                          style={{ transformOrigin: placement === 'bottom-start' ? 'left top' : 'left bottom' }}
                        >
                          <Paper>
                            <ClickAwayListener onClickAway={handleClose}>
                              <MenuList
                                autoFocusItem={open}
                                id='composition-menu'
                                aria-labelledby='composition-button'
                                onKeyDown={handleListKeyDown}
                              >
                                <MenuItem
                                  className='flex items-center gap-2'
                                  onClick={event => {
                                    handleAddQuestion('multipleChoice')
                                    handleClose(event)
                                  }}
                                >
                                  <i className='ri-radio-button-line'></i>Multiple choice
                                </MenuItem>
                                <MenuItem
                                  className='flex items-center gap-2'
                                  onClick={event => {
                                    handleAddQuestion('multipleSelection')
                                    handleClose(event)
                                  }}
                                >
                                  <i className='ri-checkbox-fill'></i>Multiple selection
                                </MenuItem>
                              </MenuList>
                            </ClickAwayListener>
                          </Paper>
                        </Grow>
                      )}
                    </Popper>
                  </Stack>
                </Box>
                {isLoadingQuestion ? (
                  <div className='w-full flex justify-center items-center'>
                    <CircularProgress />
                  </div>
                ) : (
                  questionData &&
                  questionData.map((tab, index) => (
                    <Tab
                      key={tab.questionId.toString()}
                      value={tab.questionId.toString()}
                      className={`pl-4 pr-1 hover:!pie-[2px] !min-w-full ${
                        draggedItem?.questionId === tab.questionId ? 'opacity-50' : ''
                      } ${dragOverItemId === tab.questionId ? 'border-2 border-primary border-solid' : ''} ${
                        activeTab === tab.questionId.toString() ? 'bg-primary/10' : ''
                      }`}
                      label={
                        <Box
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            width: '100%'
                          }}
                          onDragStart={e => handleDragStart(e, index)}
                          onDragOver={e => handleDragOver(e, tab.questionId)}
                          onDrop={e => handleDrop(e, index)}
                          onDragEnd={handleDragEnd}
                          draggable
                        >
                          <Typography variant='body1' className='font-bold line-clamp-1 text-left' color='text.primary'>
                            {`${index + 1}. ${renderTitle(tab)}`}
                          </Typography>

                          <i className='ri-menu-line' style={{ cursor: 'move' }}></i>
                        </Box>
                      }
                      sx={{
                        justifyContent: 'flex-start',
                        cursor: 'default'
                      }}
                    />
                  ))
                )}
                {activeTab === '0' ? (
                  <Tab
                    value={'0'}
                    label={
                      <Box
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          width: '100%'
                        }}
                        draggable
                      >
                        <Typography variant='body1' className='font-bold line-clamp-1 text-left' color='text.primary'>
                          {`${(questionData && questionData?.length + 1) || 1}. ${renderTitle(tabNewQuestion)}`}
                        </Typography>
                        <i className='ri-menu-line' style={{ cursor: 'move' }}></i>
                      </Box>
                    }
                    className='!pl-4 pr-[4px] !min-w-full'
                    sx={{
                      justifyContent: 'flex-start',
                      cursor: 'default'
                    }}
                  />
                ) : null}
              </TabList>
            </Grid>
            <Grid item xs={9} className='max-h-[calc(100vh-94px)] overflow-y-auto'>
              <FormProvider {...methodsPlan}>
                <TabPanel value={'-1'} className='p-0 h-full pb-3'>
                  <FormAdd title='Plan practice test' loading={updateExam.isLoading} onSubmit={onSubmitPlan} />
                </TabPanel>
              </FormProvider>
              {isLoadingQuestion ? (
                <div className='w-full flex justify-center items-center'>
                  <CircularProgress />
                </div>
              ) : (
                questionData &&
                questionData.map((tab, index) => (
                  <FormProvider key={tab.questionId.toString()} {...methodsNewQuestion}>
                    <TabPanel value={tab.questionId.toString()} className='pb-3'>
                      <QuestionEmptyForm
                        handleFormSubmit={onSubmitNewQuestion}
                        type={typeAdd}
                        index={index}
                        handleDeleteQuestion={onDeleteQuestion}
                        setTitle={setTitle}
                        loading={updateQuestion.isLoading}
                      />
                    </TabPanel>
                  </FormProvider>
                ))
              )}
              {activeTab === '0' && (
                <FormProvider {...methodsNewQuestion}>
                  <TabPanel value={'0'} className='pb-3'>
                    <QuestionEmptyForm
                      handleFormSubmit={onSubmitNewQuestion}
                      type={typeAdd}
                      index={questionData ? questionData?.length : 0}
                      handleDeleteQuestion={onDeleteQuestion}
                      setTitle={setTitle}
                      loading={createQuestion.isLoading}
                    />
                  </TabPanel>
                </FormProvider>
              )}
            </Grid>
          </Grid>
        </TabContext>
      )}
      <DialogCustom
        dialogTitle={dialogActive.dialogTitle}
        dialogContent={dialogActive.dialogContent}
        dialogActions={dialogActive.dialogActions}
        openDialog={openDialog}
        setOpenDialog={setOpenDialog}
      />

      <DialogCustom
        onClose={() => {
          clearErrorPublish('description')
          resetPublish({ description: '' })
        }}
        dialogTitle={'Publish Practice Test'}
        dialogContent={
          <Box>
            <Typography variant='h6' color='text.primary'>
              Version: <span className='font-bold'>{examVersionId}</span>
            </Typography>
            <Typography variant='h6' color='text.primary' className='mt-3'>
              Descriptions
            </Typography>

            <TextField
              label='Description'
              multiline
              size='medium'
              className='min-w-[560px] mt-2'
              error={!!errorPublish.description}
              helperText={errorPublish.description?.message || ''}
              {...registerPublish('description')}
              inputRef={registerPublish('description').ref}
            />
          </Box>
        }
        dialogActions={
          <>
            <Button
              onClick={() => {
                resetPublish({ description: '' })
                setIsVisible(false)
                clearErrorPublish('description')
              }}
              color='secondary'
              variant='outlined'
            >
              Cancel
            </Button>
            <Button
              color='primary'
              variant='contained'
              type='submit'
              onClick={handleSubmitPublish(handlePublishExamVersion)}
            >
              Publish
            </Button>
          </>
        }
        openDialog={isVisible}
        setOpenDialog={setIsVisible}
      />
    </Layout>
  )
}

export default PracticePlan
