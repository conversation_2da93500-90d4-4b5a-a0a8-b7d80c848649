import type { ReactElement } from 'react'
import React, { useState } from 'react'

import { Card, Divider, IconButton, Menu, MenuItem, Typography } from '@mui/material'

// Components
import TableCustom from '@/components/Table'
import Layout from '@/layouts/MainLayout'
import type { DialogContentType } from '@/types'
import DialogCustom from '@/views/Dialog'
import { useApiDelete, useApiGet, useApiPut } from '@/hooks/useApi'
import type { Rating } from '@/api/rating'
import { useToast } from '@/contexts/ToastContext'

interface MenuState {
  anchorEl: HTMLElement | null
  id: number | null
}

interface PaginatedResponse<T> {
  ratings: T[]
  meta: {
    totalCount: number
    currentPage: number
  }
}

const RatingPage = () => {
  // Table columns configuration
  const columns = [
    { id: 'shortName', label: 'Course Short Name', fixed: true },
    { id: 'user', label: 'User', fixed: true },
    { id: 'rating', label: 'Rating', fixed: false },
    { id: 'status', label: 'Status', fixed: false }
  ]

  const { showToast } = useToast()

  // States
  const [page, setPage] = useState<number>(1)
  const [rowsPerPage, setRowsPerPage] = useState<number>(10)
  const [isVisible, setIsVisible] = useState<boolean>(false)

  const [dialogContent, setDialogContent] = useState<DialogContentType>({
    dialogActions: '',
    dialogTitle: '',
    dialogContent: <></>,
    handleDialogAction: () => {}
  })

  const [menuState, setMenuState] = useState<MenuState>({
    anchorEl: null,
    id: null
  })

  const { data, isLoading } = useApiGet<PaginatedResponse<Rating>>('/reviews', 'reviews', {
    page: page
  })

  const changeStatus = useApiPut<Rating>()

  const deleteRating = useApiDelete<Rating>()

  // Computed properties
  const open = Boolean(menuState.anchorEl)

  const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    setPage(newPage + 1)
  }

  const handleReject = async () => {
    if (menuState.id === 0) {
      return
    }

    changeStatus.mutate(
      {
        endpoint: `/reviews/${menuState.id}`,
        data: { status: 'Rejected' }
      },
      {
        onSuccess: () => {
          showToast('Rating rejected successfully', 'success')
        },
        onError: (error: any) => {
          showToast(error.response?.data?.message as string, 'error')
        }
      }
    )
    setIsVisible(false)

    handleMenuClose()
  }

  const handleDelete = async () => {
    if (menuState.id === 0) {
      return
    }

    await deleteRating.mutate(
      {
        endpoint: `/reviews/${menuState.id}`
      },
      {
        onSuccess: () => {
          showToast('Rating deleted successfully', 'success')
        },
        onError: (error: any) => {
          showToast(error.response?.data?.message as string, 'error')
        }
      }
    )

    setIsVisible(false)
    handleMenuClose()
  }

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, id: number) => {
    event.stopPropagation()

    setMenuState({
      anchorEl: event.currentTarget,
      id: id
    })
  }

  const handleMenuClose = () => {
    setMenuState({
      anchorEl: null,
      id: null
    })
  }

  const handleApprove = () => {
    if (menuState.id === 0) {
      return
    }

    changeStatus.mutate(
      {
        endpoint: `/reviews/${menuState.id}`,
        data: { status: 'Approved' }
      },
      {
        onSuccess: () => {
          showToast('Rating approved successfully', 'success')
        },
        onError: (error: any) => {
          showToast(error.response?.data?.message as string, 'error')
        }
      }
    )
    handleMenuClose()

    setIsVisible(false)
  }

  const renderCell = (columnId: string, rowData: any) => {
    if (columnId === 'user') {
      return <span className='whitespace-nowrap'>{rowData.user.firstName + ' ' + rowData.user.lastName}</span>
    }

    if (columnId === 'shortName') {
      return <span className='whitespace-nowrap'>{rowData.course.shortName}</span>
    }

    return rowData[columnId]
  }

  const renderActions = (row: any) => {
    const isCurrentRow = menuState.id === Number(row.reviewId)

    const dialogList = [
      {
        id: 'delete',
        dialogContent: (
          <Typography>Are you sure you want to delete this rating? This is permanent and cannot be undone.</Typography>
        ),
        dialogTitle: 'Delete Rating',
        dialogActions: 'Delete',
        handleDialogAction: handleDelete
      },
      {
        id: 'reject',
        dialogContent: <Typography>Are you sure you want to reject this rating?</Typography>,
        dialogTitle: 'Reject Rating',
        dialogActions: 'Reject',
        handleDialogAction: handleReject
      },
      {
        id: 'approve',
        dialogContent: <Typography>Are you sure you want to approve this rating?</Typography>,
        dialogTitle: 'Approve Rating',
        dialogActions: 'Approve',
        handleDialogAction: handleApprove
      }
    ]

    return (
      <div className='flex gap-2'>
        <IconButton
          className='bg-transparent'
          id={`menu-button-${row.id}`}
          aria-controls={open && isCurrentRow ? `menu-${row.reviewId}` : undefined}
          aria-haspopup='true'
          aria-expanded={open && isCurrentRow ? 'true' : undefined}
          onClick={e => handleMenuClick(e, row.reviewId)}
        >
          <i className='ri-more-2-fill'></i>
        </IconButton>
        <Menu
          id={`menu-${row.reviewId}`}
          aria-labelledby={`menu-button-${row.reviewId}`}
          anchorEl={menuState.anchorEl}
          open={open && isCurrentRow}
          onClose={handleMenuClose}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'left'
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left'
          }}
        >
          <MenuItem
            disabled={row.status === 'Approved'}
            onClick={() => {
              setDialogContent(dialogList.find(item => item.id === 'approve')!)
              setIsVisible(true)
            }}
          >
            <Typography className='text-primary'>Approve</Typography>
          </MenuItem>
          <MenuItem
            disabled={row.status === 'Rejected'}
            onClick={() => {
              setDialogContent(dialogList.find(item => item.id === 'reject')!)
              setIsVisible(true)
            }}
          >
            <Typography className='text-textPrimary'>Rejected</Typography>
          </MenuItem>
          <MenuItem
            onClick={() => {
              setDialogContent(dialogList.find(item => item.id === 'delete')!)
              setIsVisible(true)
            }}
          >
            <Typography className='text-error'>Delete</Typography>
          </MenuItem>
        </Menu>
      </div>
    )
  }

  return (
    <>
      <Typography variant='h3' className='mb-2'>
        Rating
      </Typography>
      <Divider />
      <Card className='px-4 py-6 mt-4'>
        <TableCustom
          columns={columns}
          data={data?.ratings || []}
          selectedColumns={columns.map(col => col.id)}
          page={page - 1}
          renderCell={renderCell}
          rowsPerPage={rowsPerPage}
          handleChangePage={handleChangePage}
          handleChangeRowsPerPage={handleChangeRowsPerPage}
          actions={renderActions}
          actionType='dynamic'
          loading={isLoading}
          total={data?.meta.totalCount || 0}
          messageDataFound='No rating found'
        />
      </Card>
      <DialogCustom
        openDialog={isVisible}
        setOpenDialog={setIsVisible}
        dialogActions={dialogContent.dialogActions}
        dialogContent={dialogContent.dialogContent}
        dialogTitle={dialogContent.dialogTitle}
        handleDialogAction={dialogContent.handleDialogAction}
        handleClose={handleMenuClose}
      />
    </>
  )
}

RatingPage.getLayout = function (page: ReactElement) {
  return <Layout>{page}</Layout>
}

export default RatingPage
