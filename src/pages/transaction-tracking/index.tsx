import type { ReactElement } from 'react'
import { useState } from 'react'

// MUI Imports
import { <PERSON><PERSON>, Card, Divider, IconButton, Menu, MenuItem, TextField, Typography } from '@mui/material'

// Components Imports
import { useForm } from 'react-hook-form'

import * as yup from 'yup'

import { yupResolver } from '@hookform/resolvers/yup'

import Dropdown from '@/components/DropdownTableColumn'
import TableCustom from '@/components/Table'
import Layout from '@/layouts/MainLayout'

// Util Imports
import { formatNumber } from '@/utils'
import DialogCustom from '@/views/Dialog'
import type { MenuState } from '@/types'
import { useApiGet, useApiPut } from '@/hooks/useApi'
import { useDebounce } from '@/hooks/useDebounce'
import { useToast } from '@/contexts/ToastContext'

const transactionSchema = yup.object({
  transactionContent: yup.string().required('Reason is required')
})

const resolver = yupResolver(transactionSchema)

interface Transaction {
  transactionId: number
  sepayTransactionId: string
  transactionDate: string
  orderId: string
  amountIn: number
  amountOut: number
  transactionContent: string
  isVerified: boolean
}

interface PaginatedResponse<T> {
  transactions: T[]
  meta: {
    totalCount: number
    currentPage: number
  }
}

interface VerifyTransactionForm {
  transactionContent: string
}

const TransactionTracking = () => {
  const [menuState, setMenuState] = useState<MenuState>({
    anchorEl: null,
    id: null
  })

  const columns = [
    { id: 'transactionId', label: 'ID', fixed: true },
    { id: 'sepayTransactionId', label: 'S-Trans', fixed: true },
    { id: 'transactionDate', label: 'Trans Date', fixed: true },
    { id: 'orderId', label: 'Order ID', fixed: true },
    { id: 'amountIn', label: 'Amount In', fixed: false },
    { id: 'amountOut', label: 'Amount Out', fixed: false },
    { id: 'transactionContent', label: 'Content', fixed: false },
    { id: 'isVerified', label: 'Status', fixed: false }
  ]

  const { showToast } = useToast()

  const [selectedColumns, setSelectedColumns] = useState<string[]>(columns.map(col => col.id))
  const [page, setPage] = useState<number>(1)
  const [rowsPerPage, setRowsPerPage] = useState<number>(10)
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [openDialog, setOpenDialog] = useState<boolean>(false)
  const debouncedSearchQuery = useDebounce(searchQuery)

  const {
    handleSubmit,
    register,
    reset,
    formState: { errors, isDirty }
  } = useForm<VerifyTransactionForm>({
    resolver: resolver,
    defaultValues: {
      transactionContent: ''
    }
  })

  // Computed properties
  const open = Boolean(menuState.anchorEl)

  const { data, isLoading } = useApiGet<PaginatedResponse<Transaction>>('/transactions', 'transactions', {
    page: page,
    limit: rowsPerPage,
    orderId: debouncedSearchQuery
  })

  const verifyTransaction = useApiPut<Transaction, Partial<Transaction>>()

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, id: number) => {
    event.stopPropagation()
    setMenuState({
      anchorEl: event.currentTarget,
      id: id
    })
  }

  const handleMenuClose = () => {
    setMenuState({
      anchorEl: null,
      id: null
    })
  }

  const renderCell = (columnId: string, rowData: any) => {
    if (columnId === 'transactionId') {
      return <span className='whitespace-nowrap text-primary'>{`#${rowData[columnId]}`}</span>
    }

    if (columnId === 'amountIn' || columnId === 'amountOut') {
      return <span className='whitespace-nowrap'>{`${formatNumber(rowData[columnId])} đ`}</span>
    }

    if (columnId === 'isVerified') {
      return (
        <span className='whitespace-nowrap'>
          {rowData[columnId] ? (
            <span className='text-success'>Verified</span>
          ) : (
            <span className='text-warning'>Unverified</span>
          )}
        </span>
      )
    }

    return rowData[columnId]
  }

  const handleVerifyTransaction = async (data: VerifyTransactionForm) => {
    if (menuState.id) {
      verifyTransaction.mutate(
        {
          endpoint: `/transactions/${menuState.id}/verify`,
          data: {
            transactionContent: data.transactionContent
          }
        },
        {
          onSuccess: () => {
            showToast('Transaction verified successfully', 'success')
            setMenuState({ anchorEl: null, id: null })
            setOpenDialog(false)
            reset()
          },
          onError: (res: any) => {
            showToast(res.response?.data?.message || 'Error', 'error')
          }
        }
      )
    }
  }

  const renderActions = (row: Transaction) => {
    const isCurrentRow = menuState.id === row.transactionId

    return (
      <div className='flex gap-2'>
        <IconButton
          className='bg-transparent'
          id={`menu-button-${row.transactionId}`}
          aria-controls={open && isCurrentRow ? `menu-${row.transactionId}` : undefined}
          aria-haspopup='true'
          aria-expanded={open && isCurrentRow ? 'true' : undefined}
          onClick={e => handleMenuClick(e, Number(row.transactionId))}
          disabled={row.isVerified}
        >
          <i className='ri-more-2-fill'></i>
        </IconButton>
        <Menu
          id={`menu-${row.transactionId}`}
          aria-labelledby={`menu-button-${row.transactionId}`}
          anchorEl={menuState.anchorEl}
          open={open && isCurrentRow}
          onClose={handleMenuClose}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'left'
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left'
          }}
        >
          <MenuItem
            onClick={() => {
              setOpenDialog(true)
              setMenuState((prev: MenuState) => ({ ...prev, anchorEl: null }))
            }}
          >
            <Typography>Verify</Typography>
          </MenuItem>
        </Menu>
      </div>
    )
  }

  const handleColumnToggle = (columnId: string) => {
    setSelectedColumns(prev => (prev.includes(columnId) ? prev.filter(id => id !== columnId) : [...prev, columnId]))
  }

  const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    setPage(newPage + 1)
  }

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  return (
    <div>
      <Typography variant='h3' className='mb-2'>
        Transaction Tracking
      </Typography>
      <Divider />
      <Card className='px-4 mt-4 py-6 '>
        <div className='flex justify-between'>
          <div className='mb-4 flex gap-3 items-center h-10'>
            <TextField
              fullWidth
              type='text'
              placeholder='Order ID'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              size='small'
            />
            <Button variant='contained' className='whitespace-nowrap'>
              Search
            </Button>
          </div>
          <Dropdown columns={columns} selectedColumns={selectedColumns} onToggleColumn={handleColumnToggle} />
        </div>
        <TableCustom
          columns={columns}
          data={data?.transactions || []}
          selectedColumns={selectedColumns}
          page={page - 1}
          rowsPerPage={rowsPerPage}
          handleChangePage={handleChangePage}
          handleChangeRowsPerPage={handleChangeRowsPerPage}
          renderCell={renderCell}
          actions={renderActions}
          actionType='dynamic'
          loading={isLoading}
          messageDataFound='No transaction found'
          total={data?.meta.totalCount || 0}
        />
        <DialogCustom
          openDialog={openDialog}
          setOpenDialog={setOpenDialog}
          dialogActions='Update'
          handleDialogAction={handleSubmit(handleVerifyTransaction)}
          isDisableDialogAction={!isDirty}
          handleClose={() => {
            setOpenDialog(false)
            setMenuState({ anchorEl: null, id: null })
            reset()
          }}
          dialogContent={
            <>
              <TextField
                fullWidth
                type='text'
                label='Transaction Content'
                size='small'
                className='min-w-[560px]'
                {...register('transactionContent')}
                error={!!errors.transactionContent}
                helperText={errors.transactionContent ? errors.transactionContent.message : ''}
              />
            </>
          }
          dialogTitle='Verify'
        />
      </Card>
    </div>
  )
}

TransactionTracking.getLayout = function (page: ReactElement) {
  return <Layout>{page}</Layout>
}

export default TransactionTracking
