'use client'
import React, { useState, useEffect } from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import dynamic from 'next/dynamic'

// Dynamically import the editor component to avoid SSR issues
const EditorComponent = dynamic(() => import('@/components/EditorDemo'), {
  ssr: false,
  loading: () => <div className="p-4 text-center text-gray-500">Loading editor...</div>
})

const EditorDemo = () => {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])
  const [editorValue1, setEditorValue1] = useState('<p>Welcome to the rich text editor demo!</p>')
  const [editorValue2, setEditorValue2] = useState('')
  const [editorValue3, setEditorValue3] = useState('')

  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 5 * 60 * 1000,
            retry: 1
          }
        }
      })
  )

  if (!isClient) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading editor demo...</p>
        </div>
      </div>
    )
  }

  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
              Rich Text Editor Demo
            </h1>
            
            {/* Basic Editor */}
            <div className="mb-12">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                Basic Editor
              </h2>
              <div className="border border-gray-200 rounded-lg p-4">
                <EditorComponent
                  value={editorValue1}
                  onChange={setEditorValue1}
                  placeholder="Start typing your content here..."
                  className="min-h-[200px]"
                />
              </div>
              <div className="mt-4 p-4 bg-gray-100 rounded-lg">
                <h3 className="font-medium text-gray-700 mb-2">Editor Content (HTML):</h3>
                <pre className="text-sm text-gray-600 whitespace-pre-wrap break-all">
                  {editorValue1}
                </pre>
              </div>
            </div>

            {/* Editor with Optional Toolbar */}
            <div className="mb-12">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                Editor with Optional Toolbar (Focus to Show Toolbar)
              </h2>
              <div className="border border-gray-200 rounded-lg p-4">
                <EditorComponent
                  value={editorValue2}
                  onChange={setEditorValue2}
                  placeholder="Click here to focus and reveal the toolbar..."
                  optionalToolbar={true}
                  className="min-h-[150px]"
                />
              </div>
              <div className="mt-4 p-4 bg-gray-100 rounded-lg">
                <h3 className="font-medium text-gray-700 mb-2">Editor Content (HTML):</h3>
                <pre className="text-sm text-gray-600 whitespace-pre-wrap break-all">
                  {editorValue2 || 'No content yet...'}
                </pre>
              </div>
            </div>

            {/* Custom Styled Editor */}
            <div className="mb-12">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                Custom Styled Editor
              </h2>
              <div className="border-2 border-blue-200 rounded-lg p-4 bg-blue-50">
                <EditorComponent
                  value={editorValue3}
                  onChange={setEditorValue3}
                  placeholder="This editor has custom container styling..."
                  containerClassName="custom-editor-container"
                  className="min-h-[200px] bg-white rounded"
                />
              </div>
              <div className="mt-4 p-4 bg-gray-100 rounded-lg">
                <h3 className="font-medium text-gray-700 mb-2">Editor Content (HTML):</h3>
                <pre className="text-sm text-gray-600 whitespace-pre-wrap break-all">
                  {editorValue3 || 'No content yet...'}
                </pre>
              </div>
            </div>

            {/* Features List */}
            <div className="bg-blue-50 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-blue-900 mb-4">
                Editor Features
              </h2>
              <ul className="space-y-2 text-blue-800">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  Rich text formatting (bold, italic, underline)
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  Headers (H2, H3, H4)
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  Ordered and bullet lists
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  Links and images
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  Code and code blocks
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  Image upload with S3 integration (mocked in demo)
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  Image resizing capabilities
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  Optional toolbar (shows on focus)
                </li>
              </ul>
            </div>

            {/* Instructions */}
            <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-yellow-800 mb-3">
                How to Test
              </h3>
              <div className="text-yellow-700 space-y-2">
                <p>• Try typing and formatting text using the toolbar buttons</p>
                <p>• Test image upload by clicking the image button (uses mock upload)</p>
                <p>• Focus on the second editor to see the optional toolbar appear</p>
                <p>• Check the HTML output below each editor to see the generated markup</p>
                <p>• Try creating lists, adding links, and using code blocks</p>
              </div>
            </div>

            {/* Navigation */}
            <div className="mt-8 text-center">
              <a
                href="/"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                ← Back to Home
              </a>
            </div>
          </div>
        </div>
      </div>
    </QueryClientProvider>
  )
}

export default EditorDemo
