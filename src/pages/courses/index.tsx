import type { ReactElement } from 'react'
import { useEffect, useState } from 'react'

import { useRouter } from 'next/navigation'

import { useForm } from 'react-hook-form'
import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'

// MUI Imports
import { Button, Card, Divider, TextField, Typography } from '@mui/material'

// Components Imports
import Dropdown from '@/components/DropdownTableColumn'
import TableCustom from '@/components/Table'
import Layout from '@/layouts/MainLayout'
import DialogCustom from '@/views/Dialog'

// Util Imports
import { convertS3ToCloudFrontUrl, convertToSlug, formatNumber } from '@/utils'
import { useApiGet, useApiPost } from '@/hooks/useApi'
import type { Course } from '@/api/courses'

// import { createCourse } from '@/api/courses'
import { useDebounce } from '@/hooks/useDebounce'
import type { CourseFormData } from '@/types/pages/course'
import { useToast } from '@/contexts/ToastContext'

const columns = [
  { id: 'courseId', label: 'Course Id', fixed: true },
  { id: 'coverImage', label: 'Cover Image', fixed: true },
  { id: 'courseName', label: 'Course Name', fixed: true },
  { id: 'headline', label: 'Headline', fixed: false },
  { id: 'originalPrice', label: 'Original Price', fixed: false },
  { id: 'salePrice', label: 'Sale Price', fixed: false },
  { id: 'publishDate', label: 'Publish Date', fixed: false },
  { id: 'isPublished', label: 'Published', fixed: false }
]

const courseSchema = yup.object({
  courseName: yup
    .string()
    .trim()
    .required('Course name is required')
    .max(255, 'Course name must be less than 255 characters'),
  shortName: yup
    .string()
    .trim()
    .required('Short name is required')
    .max(50, 'Short name must be less than 50 characters'),
  slug: yup.string().trim().required('Slug is required')
})

const resolver = yupResolver(courseSchema)

interface PaginatedResponse<T> {
  courses: T[]
  meta: {
    totalCount: number
    page: number
    limit: number
  }
}

const Courses = () => {
  const { showToast } = useToast()
  const router = useRouter()

  const [searchQuery, setSearchQuery] = useState<string>('')
  const debouncedSearchQuery = useDebounce(searchQuery)

  const [selectedColumns, setSelectedColumns] = useState<string[]>(columns.map(col => col.id))
  const [page, setPage] = useState<number>(1)
  const [rowsPerPage, setRowsPerPage] = useState<number>(10)
  const [openDialog, setOpenDialog] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<CourseFormData>({
    resolver: resolver,
    defaultValues: {
      courseName: '',
      shortName: '',
      slug: ''
    }
  })

  // Watch courseName changes
  const courseName = watch('courseName')

  // Update slug when courseName changes
  useEffect(() => {
    if (courseName) {
      const newSlug = convertToSlug(courseName, '')

      setValue('slug', newSlug)
    } else {
      setValue('slug', '')
    }
  }, [courseName, setValue])

  const { data, isLoading: loading } = useApiGet<PaginatedResponse<Course>>('/courses', 'courses', {
    limit: rowsPerPage,
    page: page,
    shortName: debouncedSearchQuery
  })

  const createCourse = useApiPost<Course, Omit<Course, 'add-course'>>()

  const renderCell = (columnId: string, rowData: any) => {
    if (columnId === 'coverImage') {
      return (
        <img
          src={rowData[columnId] ? convertS3ToCloudFrontUrl(rowData[columnId]) : '/images/empty.webp'}
          alt='course'
          className='w-52 h-32 object-cover'
        />
      )
    }

    if (columnId === 'originalPrice' || columnId === 'salePrice') {
      return <span className='whitespace-nowrap'>{`${rowData[columnId] ? formatNumber(rowData[columnId]) : 0} đ`}</span>
    }

    if (columnId === 'courseName') {
      return <span className='block w-max max-w-64'>{rowData[columnId]}</span>
    }

    if (columnId === 'headline') {
      return <span className='block w-max max-w-96'>{rowData[columnId]}</span>
    }

    if (columnId === 'isPublished') {
      return <span className='whitespace-nowrap'>{rowData[columnId] ? 'Yes' : 'No'}</span>
    }

    return rowData[columnId]
  }

  const handleColumnToggle = (columnId: string) => {
    setSelectedColumns(prev => (prev.includes(columnId) ? prev.filter(id => id !== columnId) : [...prev, columnId]))
  }

  const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    setPage(newPage + 1)
  }

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newRowsPerPage = parseInt(event.target.value, 10)

    setRowsPerPage(newRowsPerPage)
    setPage(1) // Reset to first page when changing rows per page
  }

  const handleCloseModalAdd = () => {
    setOpenDialog(false)
    reset({ courseName: '', shortName: '' })
  }

  const onSubmit = async (data: CourseFormData) => {
    createCourse.mutate(
      {
        endpoint: '/courses',
        data
      },
      {
        onSuccess: () => {
          showToast('Course created successfully', 'success')
          reset({ courseName: '', shortName: '' })
          setPage(1)
        },
        onError: (res: any) => {
          showToast(res.response.data.message, 'error')
        }
      }
    )

    setOpenDialog(false)
    reset({ courseName: '', shortName: '' })
    setPage(1)
  }

  const handleDialogClose = () => {
    setOpenDialog(false)
    reset()
  }

  return (
    <div>
      <Typography variant='h3' className='mb-2'>
        Courses
      </Typography>
      <Divider />
      <Card className='px-4 mt-4 py-6 '>
        <div className='flex justify-between'>
          <div className='mb-4 flex gap-3 items-center h-10'>
            <Button variant='contained' className='whitespace-nowrap' onClick={() => setOpenDialog(true)}>
              Add New
            </Button>
            <TextField
              fullWidth
              type='text'
              placeholder='Filter course...'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              size='small'
            />
          </div>
          <Dropdown columns={columns} selectedColumns={selectedColumns} onToggleColumn={handleColumnToggle} />
        </div>
        <TableCustom
          total={data?.meta?.totalCount || 0}
          loading={loading}
          columns={columns}
          data={data?.courses || []}
          selectedColumns={selectedColumns}
          page={page - 1}
          rowsPerPage={rowsPerPage}
          handleChangePage={handleChangePage}
          handleChangeRowsPerPage={handleChangeRowsPerPage}
          renderCell={renderCell}
          actions={<i className='ri-pencil-fill'></i>}
          onActionClick={row => router.push(`course-detail/${row.courseId}`)}
          messageDataFound='No course found'
        />
        <DialogCustom
          handleClose={handleCloseModalAdd}
          openDialog={openDialog}
          setOpenDialog={handleDialogClose}
          dialogActions='Create'
          handleDialogAction={handleSubmit(onSubmit)}
          loading={createCourse.isLoading}
          dialogContent={
            <>
              <TextField
                size='small'
                fullWidth
                className='min-w-[560px]'
                label='Course Name'
                placeholder='Practice Exams | AWS Certified Solutions Architect Associate'
                error={!!errors.courseName}
                helperText={errors.courseName?.message || ''}
                slotProps={{
                  inputLabel: {
                    shrink: true
                  }
                }}
                {...register('courseName')}
              />
              <TextField
                className='mt-4'
                fullWidth
                label='Short Name'
                placeholder='AWS-SAA-C03'
                size='small'
                error={!!errors.shortName}
                slotProps={{
                  inputLabel: {
                    shrink: true
                  }
                }}
                helperText={errors.shortName?.message || ''}
                {...register('shortName')}
              />
              <TextField
                className='mt-4'
                fullWidth
                label='Slug'
                slotProps={{
                  inputLabel: {
                    shrink: true
                  }
                }}
                placeholder='Slug'
                size='small'
                error={!!errors.slug}
                helperText={errors.slug?.message || ''}
                {...register('slug')}
              />
            </>
          }
          dialogTitle='Create New Course'
        />
      </Card>
    </div>
  )
}

Courses.getLayout = function (page: ReactElement) {
  return <Layout>{page}</Layout>
}

export default Courses
