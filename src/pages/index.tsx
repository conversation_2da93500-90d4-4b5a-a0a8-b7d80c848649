// MUI Imports
import type { ReactElement } from 'react'

import Grid from '@mui/material/Grid'

// Components Imports
import Award from '@/views/dashboard/Award'
import Transactions from '@/views/dashboard/Transactions'
import WeeklyOverview from '@/views/dashboard/WeeklyOverview'
import Table from '@/views/dashboard/Table'
import Layout from '@/layouts/MainLayout'
import { useApiGet } from '@/hooks/useApi'
import type { User } from '@/api/user'

interface DataResponse {
  revenue: {
    amount: number
    currency: string
  }
  statistics: {
    users: number
    orders: number
    courses: number
    instructors: number
  }
}

interface PaginatedResponse<T> {
  users: T[]
  meta: {
    totalCount: number
    currentPage: number
  }
}

const DashboardAnalytics = () => {
  const { data, isLoading } = useApiGet<DataResponse>('/dashboard/overview', 'overview')
  const { data: users, isLoading: usersLoading } = useApiGet<PaginatedResponse<User>>('/users', 'users', { limit: 10 })

  const formatRevenue = (revenue?: { amount: number | string }) => {
    if (!revenue?.amount) return 0

    // Convert to number first, then format
    const numAmount = typeof revenue.amount === 'string' ? parseFloat(revenue.amount) : revenue.amount

    // Check if it's a valid number
    if (isNaN(numAmount)) return 0

    return Number(numAmount.toFixed(2))
  }

  return (
    <Grid container spacing={6}>
      <Grid item xs={12} md={4}>
        <Award revenue={formatRevenue(data?.revenue)} loading={isLoading} />
      </Grid>
      <Grid item xs={12} md={8} lg={8}>
        <Transactions
          loading={isLoading}
          users={data?.statistics.users}
          orders={data?.statistics.orders}
          courses={data?.statistics.courses}
          instructors={data?.statistics.instructors}
        />
      </Grid>
      <Grid item xs={12} md={6} lg={6}>
        <WeeklyOverview />
      </Grid>
      <Grid item xs={12} md={6} lg={6}>
        <Table users={users?.users || []} loading={usersLoading} />
      </Grid>
    </Grid>
  )
}

DashboardAnalytics.getLayout = function (page: ReactElement) {
  return <Layout>{page}</Layout>
}

export default DashboardAnalytics
