import type { ReactElement, SyntheticEvent } from 'react'
import { useState, useEffect } from 'react'

// Next Imports
import { useRouter } from 'next/router'

// MUI Imports
import { TabContext, TabList, TabPanel } from '@mui/lab'
import { Button, Grid, Tab } from '@mui/material'

// Component Imports
import PracticeTest from '@/views/courses/PracticeTest'
import Setting from '@/views/courses/Setting'
import Layout from '@/layouts/CourseLayout'
import FormAdd from '@/views/courses/FormAdd'
import type { Course } from '@/api/courses'
import { useApiGet, useApiPut } from '@/hooks/useApi'
import { useToast } from '@/contexts/ToastContext'

const CourseDetailPage = () => {
  const router = useRouter()
  const id = router.query.id

  const { showToast } = useToast()

  const { data: course, isLoading } = useApiGet<Course>(`/courses/${id}`, 'course-detail' + id)
  const publishCourse = useApiPut<Course>()

  const [activeTab, setActiveTab] = useState<string>('landingPage')

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedTab = localStorage.getItem('activeTab')

      if (storedTab) {
        setActiveTab(storedTab)
      }

      if (id) {
        localStorage.setItem('courseId', id as string)
      }
    }
  }, [id])

  const handleChange = (event: SyntheticEvent, value: string) => {
    setActiveTab(value)

    if (typeof window !== 'undefined') {
      localStorage.setItem('activeTab', value)
    }
  }

  const tabContentList: Record<string, ReactElement> = {
    landingPage: (
      <FormAdd id={id as string} title='Course Landing Page' buttonText='Save' course={course} isLoading={isLoading} />
    ),
    practiceTest: <PracticeTest courseId={id as string} />,
    setting: <Setting courseId={id as string} isPublished={course?.isPublished || false} />
  }

  const handlePublishCourse = async () => {
    await publishCourse.mutate(
      {
        endpoint: `/courses/${course?.courseId}/publish`,
        data: {}
      },
      {
        onSuccess: () => {
          showToast('Course published successfully', 'success')
        },
        onError: (error: any) => {
          showToast(error.response?.data?.message as string, 'error')
        }
      }
    )
  }

  return (
    <Layout courseName={course?.courseName}>
      <TabContext value={activeTab}>
        <Grid container spacing={6} sx={{ width: '100%', height: '100%' }}>
          <Grid
            item
            xs={false}
            sx={{
              width: 260
            }}
          >
            <TabList onChange={handleChange} orientation='vertical'>
              <Tab
                className='flex justify-start items-start pb-3'
                label='Landing Page'
                value='landingPage'
                sx={{ justifyContent: 'start', width: '100%', pb: 1 }}
              />
              <Tab
                className='flex justify-start items-start pb-3'
                label='Practice Test'
                value='practiceTest'
                sx={{ justifyContent: 'start', width: '100%', pb: 1 }}
              />
              <Tab
                className='flex justify-start items-start pb-3'
                label='Setting'
                value='setting'
                sx={{ justifyContent: 'start', width: '100%', pb: 1 }}
              />
            </TabList>
            <Button
              variant='contained'
              fullWidth
              sx={{ mt: 2, px: 2 }}
              onClick={handlePublishCourse}
              disabled={publishCourse.isLoading || (course && course?.isPublished) || false}
              loading={publishCourse.isLoading}
            >
              Public Course
            </Button>
          </Grid>
          <Grid
            item
            xs
            sx={{
              flexGrow: 1,
              minWidth: 0
            }}
          >
            <TabPanel value={activeTab} sx={{ p: 0 }}>
              {tabContentList[activeTab]}
            </TabPanel>
          </Grid>
        </Grid>
      </TabContext>
    </Layout>
  )
}

export default CourseDetailPage
