import type { ReactElement } from 'react'
import React, { useState } from 'react'

import { <PERSON><PERSON>, Card, Divider, IconButton, Menu, MenuItem, TextField, Typography } from '@mui/material'

// Components
import TableCustom from '@/components/Table'
import Dropdown from '@/components/DropdownTableColumn'
import Layout from '@/layouts/MainLayout'
import DialogCustom from '@/views/Dialog'
import { useDebounce } from '@/hooks/useDebounce'
import { useApiGet, useApiPut } from '@/hooks/useApi'
import type { User } from '@/api/user'
import { useToast } from '@/contexts/ToastContext'

interface MenuState {
  anchorEl: HTMLElement | null
  userId: number | null
}

interface PaginatedResponse<T> {
  users: T[]
  meta: {
    totalCount: number
    currentPage: number
  }
}

const UserPage = () => {
  // Table columns configuration
  const columns = [
    { id: 'name', label: 'Full Name', fixed: true },
    { id: 'email', label: 'Email', fixed: true },
    { id: 'status', label: 'Status', fixed: false }
  ]

  const { showToast } = useToast()

  // States
  const [selectedColumns, setSelectedColumns] = useState<string[]>(columns.map(col => col.id))
  const [page, setPage] = useState<number>(1)
  const [rowsPerPage, setRowsPerPage] = useState<number>(10)
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [dialogOpen, setDialogOpen] = useState<boolean>(false)
  const [id, setId] = useState<number | null>(null)
  const debouncedSearchQuery = useDebounce(searchQuery)

  const [menuState, setMenuState] = useState<MenuState>({
    anchorEl: null,
    userId: null
  })

  // Computed properties
  const open = Boolean(menuState.anchorEl)

  const { data, isLoading } = useApiGet<PaginatedResponse<User>>('/users', 'user', {
    page: page,
    limit: rowsPerPage,
    email: debouncedSearchQuery
  })

  const changeStatus = useApiPut<User>()

  // Event handlers
  const handleColumnToggle = (columnId: string) => {
    setSelectedColumns(prev => (prev.includes(columnId) ? prev.filter(id => id !== columnId) : [...prev, columnId]))
  }

  const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    setPage(newPage + 1)
  }

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, id: number) => {
    event.stopPropagation()
    setMenuState({
      anchorEl: event.currentTarget,
      userId: id
    })
  }

  const handleMenuClose = () => {
    setMenuState({
      anchorEl: null,
      userId: null
    })
  }

  const handleStatusChange = async () => {
    if (!id) {
      return
    }

    changeStatus.mutate(
      {
        endpoint: `/users/${id}/status`,
        data: { isActive: false }
      },
      {
        onSuccess: () => {
          showToast('User deactivate successfully')
        },
        onError: (error: any) => {
          showToast(error.response?.data?.message as string, 'error')
        }
      }
    )

    // Add your status change logic here
    handleMenuClose()
    setDialogOpen(false)
  }

  const renderCell = (columnId: string, rowData: any) => {
    if (columnId === 'name') {
      return <span className='whitespace-nowrap'>{rowData.firstName + ' ' + rowData.lastName}</span>
    }

    if (columnId === 'status') {
      return <span className='whitespace-nowrap'>{rowData.isActive ? 'Active' : 'Inactive'}</span>
    }

    return rowData[columnId]
  }

  const renderActions = (row: User) => {
    const isCurrentRow = menuState.userId === row.userId
    const isActiveUser = data?.users?.find(u => u.userId === row.userId)?.isActive

    return (
      <div className='flex gap-2'>
        <IconButton
          className='bg-transparent'
          id={`menu-button-${row.userId}`}
          aria-controls={open && isCurrentRow ? `menu-${row.userId}` : undefined}
          aria-haspopup='true'
          aria-expanded={open && isCurrentRow ? 'true' : undefined}
          disabled={!isActiveUser}
          onClick={e => handleMenuClick(e, Number(row.userId))}
        >
          <i className='ri-more-2-fill'></i>
        </IconButton>
        <Menu
          id={`menu-${row.userId}`}
          aria-labelledby={`menu-button-${row.userId}`}
          anchorEl={menuState.anchorEl}
          open={open && isCurrentRow}
          onClose={handleMenuClose}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'left'
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left'
          }}
        >
          {/* <MenuItem onClick={() => handleStatusChange(row.userId)}> */}
          <MenuItem
            onClick={() => {
              setId(row.userId)
              setDialogOpen(true)
            }}
          >
            <Typography className='text-error'>Deactivate</Typography>
          </MenuItem>
        </Menu>
      </div>
    )
  }

  return (
    <>
      <Typography variant='h3' className='mb-2'>
        User Management
      </Typography>
      <Divider />
      <Card className='px-4 py-6 mt-4'>
        <div className='flex justify-between'>
          <div className='mb-4 flex gap-3 items-center h-10'>
            <TextField
              fullWidth
              type='text'
              placeholder='Filter exam...'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              size='small'
            />
            <Button variant='contained' className='whitespace-nowrap'>
              Search
            </Button>
          </div>
          <Dropdown columns={columns} selectedColumns={selectedColumns} onToggleColumn={handleColumnToggle} />
        </div>
        <TableCustom
          columns={columns}
          data={data?.users || []}
          selectedColumns={selectedColumns}
          page={page - 1}
          renderCell={renderCell}
          rowsPerPage={rowsPerPage}
          handleChangePage={handleChangePage}
          handleChangeRowsPerPage={handleChangeRowsPerPage}
          actions={renderActions}
          actionType='dynamic'
          loading={isLoading}
          total={data?.meta.totalCount || 0}
          messageDataFound='No user found'
        />
      </Card>
      <DialogCustom
        openDialog={dialogOpen}
        setOpenDialog={setDialogOpen}
        dialogContent={
          <Typography>
            Are you sure you want to deactivate this User? This is permanent and cannot be undone.
          </Typography>
        }
        dialogTitle='Deactivate User'
        dialogActions='Deactivate'
        handleDialogAction={handleStatusChange}
        handleClose={() => {
          setDialogOpen(false)
          setMenuState({ anchorEl: null, userId: null })
          setId(null)
        }}
      />
    </>
  )
}

UserPage.getLayout = function (page: ReactElement) {
  return <Layout>{page}</Layout>
}

export default UserPage
