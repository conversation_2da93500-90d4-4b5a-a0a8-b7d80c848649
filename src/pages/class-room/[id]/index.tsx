import type { ReactElement, SyntheticEvent } from 'react'
import { useState } from 'react'

// MUI Imports
import { useRouter } from 'next/router'

import { TabContext, TabList, TabPanel } from '@mui/lab'
import { Grid, Tab } from '@mui/material'

// Component Imports
import Layout from '@/layouts/ClassroomLayout'
import ClassroomInfoForm from '@/views/class-room/ClassInfo'
import UserForm from '@/views/class-room/User'
import PracticeTestClassRoom from '@/views/class-room/PracticeTest'
import { useApiGet } from '@/hooks/useApi'
import type { ClassDetail } from '@/api/class'

const ClassroomDetailPage = () => {
  const router = useRouter()
  const id = router.query.id

  const { data, isLoading } = useApiGet<ClassDetail>(`/classes/${id}`, 'class-detail' + id)

  const [activeTab, setActiveTab] = useState<string>('classInfo')

  const handleChange = (event: SyntheticEvent, value: string) => {
    setActiveTab(value)

    if (typeof window !== 'undefined') {
      localStorage.setItem('activeTab', value)
    }
  }

  const tabContentList: Record<string, ReactElement> = {
    classInfo: (
      <ClassroomInfoForm
        id={data?.classId || null}
        buttonText='Save'
        loading={isLoading}
        initialData={{
          className: data?.className || '',
          courseIds: data?.courses?.map(c => c.courseId).filter((id): id is number => id !== undefined) || []
        }}
        title='Class Info'
      />
    ),
    user: <UserForm title='Add User to Class' classId={data?.classId || null} />,
    practiceTest: <PracticeTestClassRoom classId={data?.classId || null} />
  }

  return (
    <Layout name={data?.className || ''}>
      <TabContext value={activeTab}>
        <Grid container spacing={6} sx={{ width: '100%', height: '100%' }}>
          <Grid
            item
            xs={false}
            sx={{
              width: 260
            }}
            className='max-h-[calc(100vh-94px)] overflow-y-auto'
          >
            <TabList onChange={handleChange} orientation='vertical'>
              <Tab
                className='flex justify-start items-start pb-3'
                label='Class Info'
                value='classInfo'
                sx={{ justifyContent: 'start', width: '100%', pb: 1 }}
              />
              <Tab
                className='flex justify-start items-start pb-3'
                label='User'
                value='user'
                sx={{ justifyContent: 'start', width: '100%', pb: 1 }}
              />
              <Tab
                className='flex justify-start items-start pb-3'
                label='Practice Test'
                value='practiceTest'
                sx={{ justifyContent: 'start', width: '100%', pb: 1 }}
              />
            </TabList>
          </Grid>
          <Grid
            item
            xs
            sx={{
              flexGrow: 1,
              minWidth: 0
            }}
            className='max-h-[calc(100vh-94px)] overflow-y-auto'
          >
            <TabPanel value={activeTab} sx={{ p: 0, mb: '12px', mr: '12px' }}>
              {tabContentList[activeTab]}
            </TabPanel>
          </Grid>
        </Grid>
      </TabContext>
    </Layout>
  )
}

export default ClassroomDetailPage
