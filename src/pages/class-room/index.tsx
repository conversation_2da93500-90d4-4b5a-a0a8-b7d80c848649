import { useEffect, useRef, useState, type ReactElement } from 'react'

import { useRouter } from 'next/router'

// MUI Imports
import {
  Autocomplete,
  Box,
  Button,
  Card,
  CircularProgress,
  Divider,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'

// Components Imports
import * as yup from 'yup'

import { yupResolver } from '@hookform/resolvers/yup'

import { Controller, useForm } from 'react-hook-form'

import Layout from '@/layouts/MainLayout'
import ClassRoomCard from '@/views/class-room/ClassRoomCard'
import DialogCustom from '@/views/Dialog'
import type { DialogContentType } from '@/types'
import type { Class } from '@/api/class'
import { useApiDelete, useApiGet, useApiPost, useApiPut } from '@/hooks/useApi'
import { useDebounce } from '@/hooks/useDebounce'
import type { Course } from '@/api/courses'
import { useToast } from '@/contexts/ToastContext'

const ITEM_HEIGHT = 48
const ITEM_PADDING_TOP = 8

const MenuProps = {
  PaperProps: {
    style: {
      width: 250,
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP
    }
  }
}

interface PaginatedResponse<T> {
  courses: T[]
  meta: {
    totalCount: number
    page: number
    limit: number
  }
}

interface FormData {
  className: string
  courseIds: number[]
}

const schema = yup.object({
  className: yup.string().trim().required('Class name is required'),
  courseIds: yup.array().min(1, 'At least one course is required').required('Course is required')
})

const resolver = yupResolver(schema)

const ClassRoom = () => {
  const router = useRouter()

  const { showToast } = useToast()

  const [shortName, setShortName] = useState<string>('')
  const [className, setClassName] = useState<string>('')
  const [isVisible, setIsVisible] = useState<boolean>(false)
  const [openDialog, setOpenDialog] = useState<boolean>(false)
  const [id, setId] = useState<number | null>(0)
  const debouncedSearchQuery = useDebounce(className)

  const [dialogContent, setDialogContent] = useState<DialogContentType>({
    dialogActions: '',
    dialogTitle: '',
    dialogContent: <></>,
    handleDialogAction: () => {},
    loading: false
  })

  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
    reset,
    clearErrors
  } = useForm<FormData>({
    resolver: resolver,
    defaultValues: {
      className: '',
      courseIds: []
    }
  })

  const { data, isLoading } = useApiGet<{ activeClasses: Class[]; inactiveClasses: Class[] }>('/classes', 'classes', {
    className: debouncedSearchQuery,
    shortName: shortName
  }) as { data: { activeClasses: Class[]; inactiveClasses: Class[] } | undefined; isLoading: boolean }

  const { data: courseData, isLoading: loading } = useApiGet<PaginatedResponse<Course>>('/courses', 'courses', {
    limit: 100,
    page: 1
  }) as { data: PaginatedResponse<Course> | undefined; isLoading: boolean }

  const createClass = useApiPost<Class, FormData>()

  const deleteClass = useApiDelete<{ id: number }>()

  const changeStatus = useApiPut<Class, { isActive: boolean }>()

  const handleAddNew = () => {
    setOpenDialog(true)
  }

  const handleArchive = (id: number) => {
    setId(id)
    setIsVisible(true)
    setDialogContent(dialogList.find(item => item.id === 'archive')!)
  }

  const handleUnarchive = (id: number) => {
    setId(id)
    setIsVisible(true)
    setDialogContent(dialogList.find(item => item.id === 'unarchive')!)
  }

  const handleDelete = (id: number) => {
    setId(id)
    setDialogContent(dialogList.find(item => item.id === 'delete')!)
    setIsVisible(true)
  }

  const handleDeleteClass = async () => {
    if (!classIdRef.current) {
      setIsVisible(false)

      return
    }

    await deleteClass.mutate(
      {
        endpoint: `/classes/${classIdRef.current}`
      },
      {
        onSuccess: () => {
          showToast('Class deleted successfully', 'success')
          setId(null)
        },
        onError: (res: any) => {
          showToast(res.response.data.message, 'error')
        }
      }
    )

    setIsVisible(false)
  }

  const handleChangeStatus = (action: boolean) => {
    if (!classIdRef.current) {
      setIsVisible(false)

      return
    }

    changeStatus.mutate(
      {
        endpoint: `/classes/${classIdRef.current}`,
        data: {
          isActive: action
        }
      },
      {
        onSuccess: () => {
          showToast('Class updated successfully', 'success')
          setId(null)
        },
        onError: (res: any) => {
          showToast(res.response.data.message, 'error')
        }
      }
    )
    setIsVisible(false)
  }

  const resetForm = () => {
    reset({ className: '', courseIds: [] })
    clearErrors('className')
    clearErrors('courseIds')
  }

  const onSubmitFormAdd = async (data: FormData) => {
    await createClass.mutate(
      {
        endpoint: `classes`,
        data
      },
      {
        onSuccess: () => {
          showToast('Class created successfully', 'success')
        },
        onError: (res: any) => {
          showToast(res.response.data.message, 'error')
        }
      }
    )

    resetForm()
    setOpenDialog(false)
  }

  const dialogList = [
    {
      id: 'delete',
      dialogContent: (
        <Typography>Are you sure you want to delete this classroom? This is permanent and cannot be undone.</Typography>
      ),
      dialogTitle: 'Delete Classroom',
      dialogActions: 'Delete',
      handleDialogAction: handleDeleteClass,
      loading: deleteClass.isLoading
    },
    {
      id: 'archive',
      dialogContent: <Typography>Are you sure you want to archive this classroom?</Typography>,
      dialogTitle: 'Archive Classroom',
      dialogActions: 'Archive',
      handleDialogAction: () => handleChangeStatus(false),
      loading: changeStatus.isLoading
    },
    {
      id: 'unarchive',
      dialogContent: <Typography>Are you sure you want to unarchive this classroom?</Typography>,
      dialogTitle: 'Unarchive Classroom',
      dialogActions: 'Unarchive',
      handleDialogAction: () => handleChangeStatus(true),
      loading: changeStatus.isLoading
    }
  ]

  const classIdRef = useRef(id)

  useEffect(() => {
    classIdRef.current = id
  }, [id])

  return (
    <Box>
      <Typography variant='h3' className='mb-2'>
        Class Room
      </Typography>
      <Divider />
      <Card className='mt-4 py-6 max-w-full'>
        <Box className='flex gap-5 items-center px-4'>
          <FormControl fullWidth size='small' className='max-w-96'>
            <Autocomplete
              loading={loading}
              renderInput={params => <TextField {...params} size='small' label='Course Short Name' />}
              onChange={(event, newValue) => setShortName(newValue || '')}
              clearOnEscape
              options={courseData?.courses.map(c => c.shortName) || []}
              value={shortName}
            />
          </FormControl>
          <TextField
            size='small'
            className='max-w-96 '
            fullWidth
            label='Class Name'
            placeholder='Class Name'
            value={className}
            onChange={e => setClassName(e.target.value)}
          />
          <Button
            fullWidth
            className='max-w-36 h-10'
            variant='contained'
            startIcon={<i className='ri-search-line'></i>}
          >
            Search
          </Button>
        </Box>
        <Divider className='my-4' />
        <Box className='px-4'>
          <Typography variant='h5'>Active Class</Typography>
          <Divider />
          <Button
            onClick={handleAddNew}
            variant='outlined'
            className='mt-4'
            startIcon={<i className='ri-add-circle-line' style={{ fontSize: '24px' }}></i>}
          >
            Add New
          </Button>
          {isLoading ? (
            <Grid container spacing={4} className='mt-4 w-full items-center justify-center min-h-44'>
              <CircularProgress />
            </Grid>
          ) : (
            <>
              <Grid container spacing={4} className='mt-4'>
                {data?.activeClasses?.map((item: Class) => (
                  <Grid item key={item.classId}>
                    <ClassRoomCard
                      handleEditClassroom={() => router.push(`/class-room/${item.classId}`)}
                      {...item}
                      active={true}
                      handleArchive={handleArchive}
                      handleDelete={handleDelete}
                    />
                  </Grid>
                ))}
              </Grid>
              <Typography className='mt-4' variant='h5'>
                Archived
              </Typography>
              <Divider />
              <Grid container spacing={4} className='mt-4'>
                {data?.inactiveClasses?.map((item: Class) => (
                  <Grid item key={item.classId}>
                    <ClassRoomCard
                      handleEditClassroom={() => router.push(`/class-room/${item.classId}`)}
                      {...item}
                      active={false}
                      handleArchive={handleUnarchive}
                      handleDelete={handleDelete}
                    />
                  </Grid>
                ))}
              </Grid>
            </>
          )}
        </Box>
      </Card>
      <DialogCustom
        openDialog={isVisible}
        handleClose={() => setId(null)}
        setOpenDialog={setIsVisible}
        dialogActions={dialogContent.dialogActions}
        dialogContent={dialogContent.dialogContent}
        dialogTitle={dialogContent.dialogTitle}
        handleDialogAction={dialogContent.handleDialogAction}
        loading={dialogContent.loading}
      />
      <DialogCustom
        handleClose={resetForm}
        openDialog={openDialog}
        setOpenDialog={setOpenDialog}
        dialogActions='Create'
        handleDialogAction={handleSubmit(onSubmitFormAdd)}
        dialogContent={
          <>
            <TextField
              size='small'
              className='min-w-[560px]'
              fullWidth
              label='Class Name'
              placeholder='AWS-SAA-K18'
              error={!!errors.className}
              helperText={errors.className?.message}
              {...register('className')}
            />
            <FormControl fullWidth className='min-w-[560px] mt-4'>
              <InputLabel size='small' id='short-name-label'>
                Course Short Name
              </InputLabel>
              <Controller
                control={control}
                name='courseIds'
                render={({ field }) => (
                  <Select
                    {...field}
                    size='small'
                    labelId='short-name-label'
                    id='short-name-select'
                    multiple
                    label='Course Short Name'
                    MenuProps={MenuProps}
                    error={!!errors.courseIds}
                  >
                    {courseData?.courses.map(course => (
                      <MenuItem key={course.courseId} value={course.courseId}>
                        {course.shortName}
                      </MenuItem>
                    ))}
                  </Select>
                )}
              />

              {errors.courseIds && (
                <Typography className='text-error text-[13px] mt-1 ml-[14px]'>{errors.courseIds.message}</Typography>
              )}
            </FormControl>
          </>
        }
        dialogTitle='Add Class'
      />
    </Box>
  )
}

ClassRoom.getLayout = function (page: ReactElement) {
  return <Layout>{page}</Layout>
}

export default ClassRoom
