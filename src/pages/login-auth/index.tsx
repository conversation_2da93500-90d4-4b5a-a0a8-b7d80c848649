import { useState, useEffect } from 'react'

import { useF<PERSON>, Controller } from 'react-hook-form'

import { <PERSON>, But<PERSON>, Card, CardContent, Typography } from '@mui/material'

import { yupResolver } from '@hookform/resolvers/yup'

import * as yup from 'yup'

import { getServerMode } from '@/core/utils/serverHelpers'
import { useImageVariant } from '@/core/hooks/useImageVariant'
import Link from '@/components/Link'
import Logo from '@/components/layout/shared/Logo'
import OTPInput from '@/views/login-auth/OtpInput'
import Illustrations from '@/components/Illustrations'

interface FormValues {
  otp: string
}

const schema = yup.object().shape({
  otp: yup.string().length(8, 'Please enter all 8 digits').required('OTP is required')
})

const resolver = yupResolver(schema)

const AuthLoginPage = () => {
  const mode = getServerMode()
  const darkImg = '/images/pages/auth-v1-mask-dark.png'
  const lightImg = '/images/pages/auth-v1-mask-light.png'
  const authBackground = useImageVariant(mode, lightImg, darkImg)

  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm<FormValues>({
    resolver: resolver,
    defaultValues: { otp: '' }
  })

  const [countdown, setCountdown] = useState(30)
  const [isCounting, setIsCounting] = useState(true)

  useEffect(() => {
    if (isCounting) {
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer)
            setIsCounting(false)

            return 0
          }

          return prev - 1
        })
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [isCounting])

  const handleResend = () => {
    setCountdown(30)
    setIsCounting(true)
  }

  const onSubmit = (data: FormValues) => {
    console.log('Form submitted:', data)
  }

  return (
    <div>
      <div className='flex flex-col justify-center items-center min-bs-[100dvh] relative p-6'>
        <Card className='flex flex-col sm:is-[450px]'>
          <CardContent className='!px-10 !py-12'>
            <Link href='/' className='flex justify-center items-center mbe-6'>
              <Logo />
            </Link>
            <div className='flex flex-col'>
              <div>
                <Typography variant='h4'>{`Welcome to Exam Admin! 👋🏻`}</Typography>
                <Typography className='mbs-1'>
                  We sent a verification code to your email. Enter the code from the email in the field below.
                </Typography>
                <Typography variant='h5' color='text.secondary' className='!font-normal mt-4'>
                  Type your 8 digit security code
                </Typography>
              </div>
              <form noValidate autoComplete='off' onSubmit={handleSubmit(onSubmit)} className='flex flex-col gap-4'>
                <Box>
                  <Controller
                    name='otp'
                    control={control}
                    render={({ field }) => (
                      <OTPInput
                        value={field.value}
                        onChange={field.onChange}
                        error={!!errors.otp}
                        helperText={errors.otp?.message}
                      />
                    )}
                  />
                  <Typography variant='h5' color='text.secondary' className='!font-normal mt-4'>
                    Didn&apos;t get the code?
                  </Typography>
                </Box>
                <Button
                  fullWidth
                  variant='outlined'
                  size='large'
                  color='secondary'
                  onClick={handleResend}
                  disabled={isCounting}
                >
                  {isCounting ? `Resend code in ${countdown}s` : 'Resend code'}
                </Button>
                <Button className='mt-2' fullWidth variant='contained' size='large' type='submit'>
                  Confirm
                </Button>
              </form>
            </div>
          </CardContent>
        </Card>
        <Illustrations maskImg={{ src: authBackground }} />
      </div>
    </div>
  )
}

export default AuthLoginPage
