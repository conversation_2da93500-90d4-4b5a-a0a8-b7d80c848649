// pages/index.tsx

import { Head, Html, Main, NextScript } from 'next/document'

export default function Home() {
  const direction = 'ltr'

  return (
    <Html id='#__next' dir={direction}>
      <Head title='My Page'>
        <meta name='description' content='Page description' />
        <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/remixicon/fonts/remixicon.css' />
        <link
          href='https://fonts.googleapis.com/css2?family=Space+Mono:ital,wght@0,400;0,700;1,400;1,700&display=swap'
          rel='stylesheet'
        />
      </Head>
      <body className='flex is-full min-bs-full flex-auto flex-col'>
        <Main />
        <NextScript />
      </body>
    </Html>
  )
}
