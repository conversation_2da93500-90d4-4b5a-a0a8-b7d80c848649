import { useState, type ReactElement, type ReactNode } from 'react'

import type { AppProps } from 'next/app'

import type { NextPage } from 'next'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

import Providers from '@/components/Providers'

import '@/styles/globals.css'

// Generated Icon CSS Imports
import '@/assets/iconify-icons/generated-icons.css'

import 'react-perfect-scrollbar/dist/css/styles.css'

import { ToastProvider } from '@/contexts/ToastContext'

export type NextPageWithLayout<P = {}, IP = P> = NextPage<P, IP> & {
  getLayout?: (page: ReactElement) => ReactNode
}

type AppPropsWithLayout = AppProps & {
  Component: NextPageWithLayout
}

export default function MyApp({ Component, pageProps }: AppPropsWithLayout) {
  const getLayout = Component.getLayout ?? (page => page)

  const direction = 'ltr'

  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 5 * 60 * 1000,
            retry: 1
          }
        }
      })
  )

  return (
    <QueryClientProvider client={queryClient}>
      <ToastProvider>
        <Providers direction={direction}>
          <div>{getLayout(<Component {...pageProps} />)}</div>
        </Providers>
      </ToastProvider>
    </QueryClientProvider>
  )
}
