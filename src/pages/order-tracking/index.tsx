import type { ReactElement } from 'react'
import { useMemo, useState } from 'react'

// MUI Imports
import {
  Autocomplete,
  Box,
  Button,
  Card,
  CircularProgress,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  TextField,
  Typography
} from '@mui/material'

// Components Imports
import { Controller, useForm } from 'react-hook-form'

import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'

import Dropdown from '@/components/DropdownTableColumn'
import Layout from '@/layouts/MainLayout'

// Util Imports
import { formatNumber } from '@/utils'
import DialogCustom from '@/views/Dialog'
import CustomAvatar from '@/core/components/mui/Avatar'
import TableCustom from '@/components/Table'
import type { MenuState, SortConfig, TableColumn } from '@/types'
import UnpaidIcon from '@/components/Icons/Unpaid'
import { useApiGet, useApiPut } from '@/hooks/useApi'
import { useDebounce } from '@/hooks/useDebounce'
import { useToast } from '@/contexts/ToastContext'

type SortType = 'string' | 'number' | 'date'

interface Order {
  orderId: number
  userId: number
  courseId: number
  billingName: string
  billingEmail: string
  changeReason: string
  expiredAt: string
  transactionId: number
  orderStatus: string
  createdAt: string
  updatedAt: string
  totalAmount: number
  paymentStatus?: 'Paid' | 'Unpaid' | 'Cancelled' | 'Refunded'
  isGrantedByOperator: boolean
  user: {
    picture: string
  }
  course: {
    courseName: string
  }
}
interface PaginatedResponse<T> {
  orders: T[]
  meta: {
    totalCount: number
    currentPage: number
  }
}

interface SummaryResponse {
  paid: number
  unpaid: number
  canceled: number
  refunded: number
}

interface ChangePaymentStatusForm {
  status: 'Paid' | 'Unpaid' | 'Cancelled' | 'Refunded'
  reason: string
}

const orderSchema = yup.object({
  status: yup
    .mixed<'Paid' | 'Unpaid' | 'Cancelled' | 'Refunded'>()
    .oneOf(['Paid', 'Unpaid', 'Cancelled', 'Refunded'], 'Invalid status')
    .required('Status is required'),
  reason: yup.string().required('Reason is required')
})

const resolver = yupResolver(orderSchema)

const OrderTracking = () => {
  const columns: TableColumn[] = [
    { id: 'orderId', label: 'ID', fixed: true },
    { id: 'createdAt', label: 'Date', fixed: true, sortable: true, sortType: 'date' },
    { id: 'user', label: 'User', fixed: true },
    { id: 'course', label: 'Course', fixed: false },
    { id: 'total', label: 'Total', fixed: false },
    { id: 'paymentStatus', label: 'Payment', fixed: false, sortable: true, sortType: 'string' },
    { id: 'expiredAt', label: 'Expired', fixed: false, sortable: true, sortType: 'date' }
  ]

  const [menuState, setMenuState] = useState<MenuState>({
    anchorEl: null,
    id: null
  })

  const { showToast } = useToast()

  const {
    handleSubmit,
    register,
    reset,
    setValue,
    formState: { errors, isDirty },
    control
  } = useForm<ChangePaymentStatusForm>({
    resolver: resolver,
    defaultValues: {
      status: 'Paid',
      reason: ''
    }
  })

  const [selectedColumns, setSelectedColumns] = useState<string[]>(columns.map(col => col.id))
  const [page, setPage] = useState<number>(1)
  const [rowsPerPage, setRowsPerPage] = useState<number>(10)
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [openDialog, setOpenDialog] = useState<boolean>(false)
  const debouncedSearchQuery = useDebounce(searchQuery)

  const { data, isLoading } = useApiGet<PaginatedResponse<Order>>('/orders', 'orders', {
    page: page,
    limit: rowsPerPage,
    orderId: debouncedSearchQuery
  })

  const { data: dataSummary, isLoading: isLoadingSummary } = useApiGet<SummaryResponse>('/orders/summary', 'orders')
  const changePaymentStatus = useApiPut<Order, Partial<Order>>()

  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: 'date',
    direction: 'desc'
  })

  const compareValues = (a: any, b: any, sortType?: SortType) => {
    if (!sortType) return 0

    switch (sortType) {
      case 'date':
        return new Date(a).getTime() - new Date(b).getTime()
      case 'number':
        return Number(a) - Number(b)
      case 'string':
        return String(a).localeCompare(String(b))
      default:
        return 0
    }
  }

  const handleSort = (columnId: string, direction: 'asc' | 'desc') => {
    setSortConfig({ key: columnId, direction })
  }

  const sortedData = useMemo(() => {
    const column = columns.find(col => col.id === sortConfig.key)

    return [...(data?.orders || [])].sort((a, b) => {
      const result = compareValues(
        a[sortConfig.key as keyof typeof a],
        b[sortConfig.key as keyof typeof b],
        column?.sortType
      )

      return sortConfig.direction === 'asc' ? result : -result
    })
  }, [data, sortConfig])

  const totalOrder = useMemo(() => {
    return [
      { id: 1, data: dataSummary?.paid, style: 'success', status: 'Paid', icon: 'ri-checkbox-circle-fill' },
      { id: 2, data: dataSummary?.unpaid, style: 'warning', status: 'Unpaid' },
      { id: 3, data: dataSummary?.canceled, style: 'secondary', status: 'Canceled', icon: 'ri-close-circle-fill' },
      { id: 4, data: dataSummary?.refunded, style: 'secondary', status: 'Refunded', icon: 'ri-error-warning-fill' }
    ]
  }, [dataSummary])

  // Computed properties
  const open = Boolean(menuState.anchorEl)

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, data: Order) => {
    event.stopPropagation()
    setMenuState({
      anchorEl: event.currentTarget,
      id: data.orderId
    })
    setValue('status', data.paymentStatus || 'Paid')
  }

  const handleMenuClose = () => {
    setMenuState((prev: MenuState) => ({
      ...prev,
      anchorEl: null
    }))
  }

  const renderActions = (row: any) => {
    const isCurrentRow = menuState.id === row.orderId

    return (
      <div className='flex gap-2'>
        <IconButton
          className='bg-transparent'
          id={`menu-button-${row.orderId}`}
          aria-controls={open && isCurrentRow ? `menu-${row.orderId}` : undefined}
          aria-haspopup='true'
          aria-expanded={open && isCurrentRow ? 'true' : undefined}
          onClick={e => handleMenuClick(e, row)}
        >
          <i className='ri-more-2-fill'></i>
        </IconButton>
        <Menu
          id={`menu-${row.orderId}`}
          aria-labelledby={`menu-button-${row.orderId}`}
          anchorEl={menuState.anchorEl}
          open={open && isCurrentRow}
          onClose={handleMenuClose}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'left'
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left'
          }}
        >
          <MenuItem
            onClick={() => {
              handleMenuClose()
              setOpenDialog(true)
            }}
          >
            <Typography variant='body1' className='text-textPrimary'>
              Change status
            </Typography>
          </MenuItem>
        </Menu>
      </div>
    )
  }

  const handleColumnToggle = (columnId: string) => {
    setSelectedColumns(prev => (prev.includes(columnId) ? prev.filter(id => id !== columnId) : [...prev, columnId]))
  }

  const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    setPage(newPage + 1)
  }

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  const renderColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'success'
      case 'unpaid':
        return 'warning'
      default:
        return 'secondary'
    }
  }

  const renderCell = (columnId: string, rowData: any) => {
    if (columnId === 'orderId') {
      return <span className='whitespace-nowrap text-primary'>{`#${rowData[columnId]}`}</span>
    }

    if (columnId === 'user') {
      return (
        <Box className='flex items-center gap-2'>
          <CustomAvatar className='cursor-text' src={rowData.user.picture} size={34} />
          <Box>
            <Typography variant='body1' className='text-textPrimary'>
              {rowData.billingName}
            </Typography>
            <Typography variant='body2' className='underline'>
              {rowData.billingEmail}
            </Typography>
          </Box>
        </Box>
      )
    }

    if (columnId === 'total') {
      return <span>{`${formatNumber(rowData.totalAmount)}đ`}</span>
    }

    if (columnId === 'payment') {
      return (
        <Box className='flex items-center gap-2'>
          <i className={`ri-checkbox-blank-circle-fill text-xs text-${renderColor(rowData[columnId])}`}></i>
          <span className={`whitespace-nowrap text-${renderColor(rowData[columnId])}`}>{rowData[columnId]}</span>
        </Box>
      )
    }

    if (columnId === 'course') {
      return <span>{rowData.course?.courseName || '-'}</span>
    }

    return rowData[columnId]
  }

  const onChangeStatus = (data: ChangePaymentStatusForm) => {
    changePaymentStatus.mutate(
      {
        endpoint: `/orders/${menuState.id}`,
        data: {
          paymentStatus: data.status,
          changeReason: data.reason
        }
      },
      {
        onSuccess: () => {
          showToast('Change payment status successfully', 'success')
          setOpenDialog(false)
          reset()
        },
        onError: (res: any) => {
          showToast(res.response?.data?.message || 'Error', 'error')
        }
      }
    )
  }

  return (
    <div>
      <Card>
        <Box className='grid grid-cols-4 p-4'>
          {isLoadingSummary ? (
            <CircularProgress />
          ) : (
            <>
              {totalOrder.map((item, index) => (
                <Box key={item.id} className='flex'>
                  <Box className='flex flex-1 justify-between items-start p-4'>
                    <Box>
                      <Typography variant='h4'>{formatNumber(item?.data || 0)}</Typography>
                      <Typography variant='body1' className={`text-${item.style}`}>
                        {item.status}
                      </Typography>
                    </Box>
                    <CustomAvatar variant='rounded' color='secondary' className='shadow-xs'>
                      {item.icon ? <i className={item.icon}></i> : <UnpaidIcon className='w-6 h-6' />}
                    </CustomAvatar>
                  </Box>
                  {index !== totalOrder.length - 1 && <Divider orientation='vertical' flexItem />}
                </Box>
              ))}
            </>
          )}
        </Box>
      </Card>
      <Card className='px-4 mt-4 py-6 '>
        <div className='flex justify-between'>
          <div className='mb-4 flex gap-3 items-center h-10'>
            <TextField
              fullWidth
              type='text'
              placeholder='Order ID'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              size='small'
            />
            <Button variant='contained' className='whitespace-nowrap'>
              Search
            </Button>
          </div>
          <Dropdown columns={columns} selectedColumns={selectedColumns} onToggleColumn={handleColumnToggle} />
        </div>
        <TableCustom
          data={sortedData || []}
          columns={columns}
          selectedColumns={selectedColumns}
          handleChangePage={handleChangePage}
          handleChangeRowsPerPage={handleChangeRowsPerPage}
          page={page - 1}
          rowsPerPage={rowsPerPage}
          actionType='dynamic'
          actions={renderActions}
          sortConfig={sortConfig}
          handleSort={handleSort}
          renderCell={renderCell}
          loading={isLoading}
          total={data?.meta.totalCount || 0}
          messageDataFound='No order found'
        />
        <DialogCustom
          openDialog={openDialog}
          setOpenDialog={setOpenDialog}
          handleClose={() => {
            setMenuState((prev: MenuState) => ({
              ...prev,
              id: null
            }))
            reset()
          }}
          loading={changePaymentStatus.isLoading}
          handleDialogAction={handleSubmit(onChangeStatus)}
          dialogActions='Change'
          isDisableDialogAction={!isDirty}
          dialogContent={
            <>
              <Controller
                name='status'
                control={control}
                render={({ field }) => (
                  <Autocomplete
                    {...field}
                    options={['Paid', 'Unpaid', 'Cancelled', 'Refunded']}
                    onChange={(_, value) => {
                      field.onChange(value)
                    }}
                    renderInput={params => (
                      <TextField
                        {...params}
                        size='small'
                        label='Status'
                        error={!!errors.status}
                        helperText={errors.status?.message}
                        className='capitalize'
                      />
                    )}
                  />
                )}
              />
              <TextField
                label='Reason'
                size='medium'
                multiline
                className='min-w-[560px] mt-4 mb-8'
                {...register('reason')}
                error={!!errors.reason}
                helperText={errors.reason?.message}
              />
            </>
          }
          dialogTitle='Change payment status'
        />
      </Card>
    </div>
  )
}

OrderTracking.getLayout = function (page: ReactElement) {
  return <Layout>{page}</Layout>
}

export default OrderTracking
