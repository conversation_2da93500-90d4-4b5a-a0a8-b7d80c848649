'use client'
import type { ReactElement } from 'react'
import React, { useState } from 'react'

import { <PERSON><PERSON>, <PERSON>, Divider, TextField, Typography } from '@mui/material'

// Components
import TableCustom from '@/components/Table'
import Dropdown from '@/components/DropdownTableColumn'
import Layout from '@/layouts/MainLayout'
import DialogCustom from '@/views/instructor/Dialog'
import { useApiGet } from '@/hooks/useApi'
import type { Instructor } from '@/api/instructor'
import { useDebounce } from '@/hooks/useDebounce'

interface PaginatedResponse<T> {
  instructors: T[]
  meta: {
    totalCount: number
    currentPage: number
  }
}

const InstructorPage = () => {
  // Table columns configuration
  const columns = [
    { id: 'name', label: 'Full Name', fixed: true },
    { id: 'email', label: 'Email', fixed: false },
    { id: 'status', label: 'Status', fixed: false }
  ]

  // States
  const [selectedColumns, setSelectedColumns] = useState<string[]>(columns.map(col => col.id))
  const [page, setPage] = useState<number>(1)
  const [rowsPerPage, setRowsPerPage] = useState<number>(10)
  const [searchQuery, setSearchQuery] = useState<string>('')
  const debouncedSearchQuery = useDebounce(searchQuery)
  const [id, setId] = useState<number | null>(null)

  const [isOpenDialog, setOpenDialog] = useState<boolean>(false)

  // Computed properties

  const { data, isLoading } = useApiGet<PaginatedResponse<Instructor>>('/instructors', 'instructors', {
    page: page,
    limit: rowsPerPage,
    email: debouncedSearchQuery
  })

  const { data: instructor, isLoading: loading } = useApiGet<Instructor>(`/instructors/${id}`, 'instructor' + id)

  // Event handlers
  const handleColumnToggle = (columnId: string) => {
    setSelectedColumns(prev => (prev.includes(columnId) ? prev.filter(id => id !== columnId) : [...prev, columnId]))
  }

  const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    setPage(newPage + 1)
  }

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  const renderCell = (columnId: string, rowData: any) => {
    if (columnId === 'name') {
      return <span className='whitespace-nowrap'>{rowData.firstName + ' ' + rowData.lastName}</span>
    }

    if (columnId === 'status') {
      return <span className='whitespace-nowrap'>{rowData.is_active ? 'Active' : 'Inactive'}</span>
    }

    return rowData[columnId]
  }

  const renderActions = (row: Instructor) => {
    return (
      <div className='flex gap-2 justify-end'>
        <Button
          className='bg-transparent'
          variant='outlined'
          color='primary'
          onClick={() => {
            setId(row.instructorId)
            setOpenDialog(true)
          }}
        >
          View
        </Button>
      </div>
    )
  }

  return (
    <>
      <Typography variant='h3' className='mb-2'>
        Instructor Management
      </Typography>
      <Divider />
      <Card className='px-4 py-6 mt-4'>
        <div className='flex justify-between'>
          <div className='mb-4 flex gap-3 items-center h-10'>
            <TextField
              fullWidth
              type='text'
              placeholder='Filter email...'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              size='small'
            />
            <Button variant='contained' className='whitespace-nowrap'>
              Search
            </Button>
          </div>
          <Dropdown columns={columns} selectedColumns={selectedColumns} onToggleColumn={handleColumnToggle} />
        </div>
        <TableCustom
          columns={columns}
          data={data?.instructors || []}
          selectedColumns={selectedColumns}
          page={page - 1}
          renderCell={renderCell}
          rowsPerPage={rowsPerPage}
          handleChangePage={handleChangePage}
          handleChangeRowsPerPage={handleChangeRowsPerPage}
          actions={renderActions}
          actionType='dynamic'
          loading={isLoading}
          total={data?.meta.totalCount || 0}
          messageDataFound='No instructor found'
        />
      </Card>
      <DialogCustom
        openDialog={isOpenDialog}
        setOpenDialog={setOpenDialog}
        info={instructor ?? null}
        isLoading={loading}
      />
    </>
  )
}

InstructorPage.getLayout = function (page: ReactElement) {
  return <Layout>{page}</Layout>
}

export default InstructorPage
