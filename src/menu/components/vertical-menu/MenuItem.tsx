'use client'

// React Imports
import { forwardRef, useEffect, useState } from 'react'
import type { AnchorHTMLAttributes, ForwardRefRenderFunction, ReactElement, ReactNode } from 'react'

// Next Imports
import { usePathname } from 'next/navigation'

// Third-party Imports
import classnames from 'classnames'
import { useUpdateEffect } from 'react-use'
import type { CSSObject } from '@emotion/styled'

// Type Imports
import type { ChildrenType, MenuItemElement, MenuItemExactMatchUrlProps, RootStylesType } from '@/menu/types'

// Component Imports
import MenuButton from '@/menu/components/vertical-menu/MenuButton'

// Hook Imports
import useVerticalMenu from '@/menu/hooks/useVerticalMenu'
import useVerticalNav from '@/menu/hooks/useVerticalNav'

// Util Imports
import { menuClasses } from '@/menu/utils/menuClasses'
import { renderMenuIcon } from '@/menu/utils/menuUtils'

// Styled Component Imports
import StyledVerticalMenuItem from '@/menu/styles/vertical/StyledVerticalMenuItem'
import StyledMenuPrefix from '@/menu/styles/StyledMenuPrefix'
import StyledMenuLabel from '@/menu/styles/StyledMenuLabel'
import StyledMenuSuffix from '@/menu/styles/StyledMenuSuffix'

export type MenuItemProps = Omit<AnchorHTMLAttributes<HTMLAnchorElement>, 'prefix'> &
  RootStylesType &
  Partial<ChildrenType> &
  MenuItemExactMatchUrlProps & {
    icon?: ReactElement
    prefix?: ReactNode
    suffix?: ReactNode
    disabled?: boolean
    target?: string
    rel?: string
    onActiveChange?: (active: boolean) => void

    /**
     * @ignore
     */
    level?: number
  }

const MenuItem: ForwardRefRenderFunction<HTMLLIElement, MenuItemProps> = (props, ref) => {
  // Props
  const {
    children,
    icon,
    className,
    prefix,
    suffix,
    level = 0,
    disabled = false,
    onActiveChange,
    rootStyles,
    ...rest
  } = props

  // States
  const [active, setActive] = useState(false)

  // Hooks
  const pathname = usePathname()
  const { menuItemStyles, renderExpandedMenuItemIcon, textTruncate } = useVerticalMenu()

  const { toggleVerticalNav, isToggled, isBreakpointReached } = useVerticalNav()

  // Get the styles for the specified element.
  const getMenuItemStyles = (element: MenuItemElement): CSSObject | undefined => {
    if (menuItemStyles) {
      const params = { level, disabled, active, isSubmenu: false }
      const styleFunction = menuItemStyles[element]

      return typeof styleFunction === 'function' ? styleFunction(params) : styleFunction
    }
  }

  // Handle the click event.
  const handleClick = () => {
    if (isToggled) {
      toggleVerticalNav()
    }
  }

  // Change active state when the url changes
  useEffect(() => {
    const { href } = rest

    if (href) {
      const activeRules: { [key: string]: string[] } = {
        '/courses': ['/courses', '/course/add-new'],
        '/': ['/', '/profile']
      }

      const checkActive = (href: string) => {
        const relatedPaths = activeRules[href] || []

        return pathname === href || relatedPaths.some(path => pathname.startsWith(path) && path !== '/')
      }

      setActive(checkActive(href))
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, rest.href])

  // Call the onActiveChange callback when the active state changes.
  useUpdateEffect(() => {
    onActiveChange?.(active)
  }, [active])

  return (
    <StyledVerticalMenuItem
      ref={ref}
      className={classnames(
        menuClasses.menuItemRoot,
        { [menuClasses.disabled]: disabled },
        { [menuClasses.active]: active },
        className
      )}
      level={level}
      disabled={disabled}
      buttonStyles={getMenuItemStyles('button')}
      menuItemStyles={getMenuItemStyles('root')}
      rootStyles={rootStyles}
    >
      <MenuButton
        className={classnames(menuClasses.button, { [menuClasses.active]: active })}
        tabIndex={disabled ? -1 : 0}
        {...rest}
        onClick={e => {
          handleClick()
          rest.onClick && rest.onClick(e)
        }}
      >
        {renderMenuIcon({
          icon,
          level,
          active,
          disabled,
          renderExpandedMenuItemIcon,
          styles: getMenuItemStyles('icon'),
          isBreakpointReached
        })}
        {prefix && (
          <StyledMenuPrefix className={menuClasses.prefix} rootStyles={getMenuItemStyles('prefix')}>
            {prefix}
          </StyledMenuPrefix>
        )}
        <StyledMenuLabel
          className={menuClasses.label}
          rootStyles={getMenuItemStyles('label')}
          textTruncate={textTruncate}
        >
          {children}
        </StyledMenuLabel>
        {suffix && (
          <StyledMenuSuffix className={menuClasses.suffix} rootStyles={getMenuItemStyles('suffix')}>
            {suffix}
          </StyledMenuSuffix>
        )}
      </MenuButton>
    </StyledVerticalMenuItem>
  )
}

export default forwardRef(MenuItem)
