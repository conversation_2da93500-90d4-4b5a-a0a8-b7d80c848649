// Common classes for menu components
export const menuClasses = {
  root: 'ts-menu-root',
  menuSectionRoot: 'ts-menusection-root',
  menuItemRoot: 'ts-menuitem-root',
  subMenuRoot: 'ts-submenu-root',
  button: 'ts-menu-button',
  prefix: 'ts-menu-prefix',
  suffix: 'ts-menu-suffix',
  label: 'ts-menu-label',
  icon: 'ts-menu-icon',
  menuSectionWrapper: 'ts-menu-section-wrapper',
  menuSectionContent: 'ts-menu-section-content',
  menuSectionLabel: 'ts-menu-section-label',
  subMenuContent: 'ts-submenu-content',
  subMenuExpandIcon: 'ts-submenu-expand-icon',
  disabled: 'ts-disabled',
  active: 'ts-active',
  open: 'ts-open'
}

// Classes for vertical navigation menu
export const verticalNavClasses = {
  root: 'ts-vertical-nav-root',
  container: 'ts-vertical-nav-container',
  bgColorContainer: 'ts-vertical-nav-bg-color-container',
  header: 'ts-vertical-nav-header',
  backdrop: 'ts-vertical-nav-backdrop',
  toggled: 'ts-toggled',
  breakpointReached: 'ts-breakpoint-reached'
}
