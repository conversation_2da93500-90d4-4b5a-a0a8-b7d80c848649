// Third-party Imports
import styled from '@emotion/styled'
import type { CSSObject } from '@emotion/styled'

// Config Imports
import themeConfig from '@/configs/themeConfig'

// Util Imports
import { verticalLayoutClasses } from '@/utils/layoutClasses'

type StyledFooterProps = {
  overrideStyles?: CSSObject
}

const StyledFooter = styled.footer<StyledFooterProps>`
  margin-inline: auto;

  & .${verticalLayoutClasses.footerContentWrapper} {
    padding-block: 15px;
    padding-inline: ${themeConfig.layoutPadding}px;
  }

  ${({ overrideStyles }) => overrideStyles}
`

export default StyledFooter
