@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --border-radius: var(--mui-shape-borderRadius);
  --border-color: var(--mui-palette-divider);
  --primary-color: var(--mui-palette-primary-main);
  --background-color: var(--mui-palette-background-default);
  --header-height: 64px;
  --header-z-index: var(--mui-zIndex-appBar);
  --footer-z-index: 10;
  --customizer-z-index: var(--mui-zIndex-drawer);
  --search-z-index: var(--mui-zIndex-tooltip);
  --drawer-z-index: var(--mui-zIndex-drawer);
  --backdrop-color: rgb(var(--mui-mainColorChannels-light) / 0.5);
}

[data-mui-color-scheme='dark'] {
  --backdrop-color: rgb(21 16 43 / 0.6);
}

*,
::before,
::after {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border-width: 0;
  border-style: solid;
  border-color: theme('borderColor.DEFAULT', currentColor);
}

html {
  display: flex;
  inline-size: 100%;
  min-block-size: 100%;
}

a {
  color: inherit;
  text-decoration: none;
}

ul:not([class]) {
  margin-block: 1rem;
  padding-inline-start: 40px;
}

.ps__rail-y {
  inset-inline-end: 0 !important;
  inset-inline-start: auto !important;
  & .ps__thumb-y {
    inset-inline-end: 3px !important;
    inset-inline-start: auto !important;
    background-color: var(--mui-palette-divider);
    inline-size: 6px;
    &:hover,
    &:focus,
    &.ps--clicking {
      background-color: var(--mui-palette-action-disabled) !important;
    }
  }
  &:hover,
  &:focus,
  &.ps--clicking {
    background-color: var(--mui-palette-action-hover) !important;
  }
  &:hover,
  &:focus,
  &.ps--clicking {
    .ps__thumb-y {
      background-color: var(--mui-palette-action-disabled) !important;
    }
  }
}

.ts-vertical-nav-root {
  .ps__thumb-y {
    inline-size: 4px;
    &:hover,
    &:focus,
    &.ps--clicking {
      inline-size: 6px;
    }
  }
  .ps__rail-y {
    inline-size: 10px;
    &:hover,
    &:focus,
    &.ps--clicking {
      background-color: transparent !important;
      .ps__thumb-y {
        inline-size: 6px;
      }
    }
  }
}

:where([class^='ri-']) {
  font-size: 1.5rem;
}

/* components/EditorStyles.css */

.custom-quill-editor {
  /* Container styles */
  --primary-main: #8c57ff;
  --primary-light: #a379ff;
  --primary-dark: #7e4ee6;
  --primary-lighter-opacity: rgb(140, 87, 255, 0.08);
  --primary-light-opacity: rgb(140, 87, 255, 0.16);
  --primary-main-opacity: rgb(140, 87, 255, 0.24);
  --primary-dark-opacity: rgb(140, 87, 255, 0.32);
  --primary-darker-opacity: rgb(140, 87, 255, 0.38);
}

/* Reset default Quill colors */
.custom-quill-editor .ql-snow .ql-stroke {
  stroke: var(--mui-palette-text-primary) !important;
  font-weight: 400;
  stroke-width: 1.5px;
}

.custom-quill-editor .ql-snow .ql-fill {
  fill: var(--mui-palette-text-primary);
  font-weight: 400;
}

.custom-quill-editor .ql-snow .ql-picker {
  color: var(--mui-palette-text-primary);
  font-weight: 400;
}

/* Hover states for all toolbar items */
.custom-quill-editor .ql-snow .ql-toolbar button:hover,
.custom-quill-editor .ql-snow.ql-toolbar button:hover {
  color: var(--primary-main) !important;
}

.custom-quill-editor .ql-snow .ql-toolbar button:hover .ql-stroke,
.custom-quill-editor .ql-snow.ql-toolbar button:hover .ql-stroke,
.custom-quill-editor .ql-snow .ql-toolbar button:hover .ql-stroke-miter,
.custom-quill-editor .ql-snow.ql-toolbar button:hover .ql-stroke-miter {
  stroke: var(--primary-main) !important;
}

.custom-quill-editor .ql-snow .ql-toolbar button:hover .ql-fill,
.custom-quill-editor .ql-snow.ql-toolbar button:hover .ql-fill {
  fill: var(--primary-main) !important;
}

/* Active states for all toolbar items */
.custom-quill-editor .ql-snow .ql-toolbar button.ql-active,
.custom-quill-editor .ql-snow.ql-toolbar button.ql-active {
  color: var(--primary-main) !important;
}

.custom-quill-editor .ql-snow .ql-toolbar button.ql-active .ql-stroke,
.custom-quill-editor .ql-snow.ql-toolbar button.ql-active .ql-stroke,
.custom-quill-editor .ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,
.custom-quill-editor .ql-snow.ql-toolbar button.ql-active .ql-stroke-miter {
  stroke: var(--primary-main) !important;
}

.custom-quill-editor .ql-snow .ql-toolbar button.ql-active .ql-fill,
.custom-quill-editor .ql-snow.ql-toolbar button.ql-active .ql-fill {
  fill: var(--primary-main) !important;
}

/* Picker hover and active states */
.custom-quill-editor .ql-snow .ql-picker-label:hover {
  color: var(--primary-main) !important;
}

.custom-quill-editor .ql-snow .ql-picker-label.ql-active {
  color: var(--primary-main) !important;
}

.custom-quill-editor .ql-snow .ql-picker-label:hover .ql-stroke {
  stroke: var(--primary-main) !important;
}

/* Dropdown items */
.custom-quill-editor .ql-snow .ql-picker-options .ql-picker-item:hover {
  color: var(--primary-main) !important;
  background-color: var(--primary-lighter-opacity);
}

.custom-quill-editor .ql-snow .ql-picker-options .ql-picker-item.ql-selected {
  color: var(--primary-main);
}

/* Force override for SVG colors */
.custom-quill-editor .ql-snow.ql-toolbar button:hover svg,
.custom-quill-editor .ql-snow .ql-toolbar button:hover svg {
  stroke: var(--primary-main) !important;
  fill: var(--primary-main) !important;
}

.custom-quill-editor .ql-snow.ql-toolbar button.ql-active svg,
.custom-quill-editor .ql-snow .ql-toolbar button.ql-active svg {
  stroke: var(--primary-main) !important;
  fill: var(--primary-main) !important;
}

/* Background hover effect */
.custom-quill-editor .ql-snow.ql-toolbar button:hover,
.custom-quill-editor .ql-snow .ql-toolbar button:hover {
  background-color: var(--primary-lighter-opacity);
  border-radius: 4px;
}

/* Toolbar container */
.custom-quill-editor .ql-toolbar.ql-snow {
  background-color: transparent;
  border-width: 1px;
  border-color: var(--mui-palette-divider);
  border-style: solid;
}

/* Editor container */
.custom-quill-editor .ql-container.ql-snow {
  background-color: transparent;
  border-width: 1px;
  border-color: var(--mui-palette-divider);
  border-style: solid;
}

/* Default state for header dropdown icon */
.custom-quill-editor .ql-snow .ql-picker.ql-header .ql-picker-label svg,
.custom-quill-editor .ql-snow .ql-picker.ql-header .ql-picker-label:hover svg {
  stroke: #444 !important;
}

.dark .ql-toolbar .ql-picker-options {
  background-color: var(--mui-palette-background-paper); /* Nền tối */
  border: 1px solid #44475a; /* Viền dropdown */
}

.dark .ql-toolbar .ql-picker-item {
  color: #ffffff; /* Màu chữ trong dropdown */
}

.dark .ql-toolbar .ql-picker-item:hover {
  background-color: #44475a; /* Nền khi hover */
}

.dark .ql-snow .ql-picker-options .ql-picker-item.ql-selected {
  color: var(--mui-palette-primary-main) !important;
}

.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  inline-size: 114px;
}

.ql-toolbar.ql-snow .ql-picker-label {
  border: 1px solid !important;
  border-color: var(--mui-palette-divider) !important;
  border-radius: 4px !important;
}

.ql-snow .ql-picker.ql-header {
  inline-size: 114px !important;
}

.ql-snow .ql-picker {
  font-size: 15px;
}

.custom-quill-editor .ql-editor {
  font-size: 16px;
  color: #2e263de6;
  line-height: 30px;
}

.quill .ql-tooltip.ql-editing {
  position: absolute;
  inset-inline-start: 15px !important;
}

.quill code {
  background: #f8fafb !important;
  color: #2e263de6 !important;
  border: 1px solid #d1d7dc;
  font-family: 'Space Mono', serif !important;
  font-size: 16px !important;
}

.quill .ql-syntax {
  background: #f8fafb !important;
  color: #2e263de6 !important;
  border: 1px solid #d1d7dc;
  font-family: 'Space Mono', serif !important;
}

.optional-toolbar .ql-toolbar.ql-snow {
  display: none;
}

.optional-toolbar .ql-container.ql-snow {
  border-block-start: 1px solid var(--mui-palette-divider) !important;
}

code {
  font-family: inherit;
  padding-block: 2px;
  padding-inline: 4px;
  border-radius: 4px;
  font-size: 90%;
  color: var(--mui-palette-info-main);
  background-color: rgb(var(--mui-palette-info-mainChannel) / 0.08);
  border: 0;
}
