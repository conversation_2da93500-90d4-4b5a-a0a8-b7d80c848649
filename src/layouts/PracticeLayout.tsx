// Layout Imports
import LayoutWrapper from '@/components/layout/LayoutWrapper'

// Component Imports
import Providers from '@/components/Providers'
import CourseLayout from '@/components/layout/CourseLayout'
import Navbar from '@/components/layout/practice-test/NavbarBtn'

interface Props {
  name: string
  children: React.ReactNode
  handlePublish?: () => void
  disableBtn?: boolean
}

const Layout: React.FC<Props> = ({ children, name, handlePublish, disableBtn = false }) => {
  // Vars
  const direction = 'ltr'

  return (
    <Providers direction={direction}>
      <LayoutWrapper
        verticalLayout={
          <CourseLayout navbar={<Navbar handlePublish={handlePublish} name={name} disableBtn={disableBtn} />}>
            {children}
          </CourseLayout>
        }
      />
    </Providers>
  )
}

export default Layout
