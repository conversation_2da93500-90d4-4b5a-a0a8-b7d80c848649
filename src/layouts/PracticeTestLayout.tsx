// Type Imports
import type { ChildrenType } from '@/core/types'

// Layout Imports
import LayoutWrapper from '@/components/layout/LayoutWrapper'

// Component Imports
import Providers from '@/components/Providers'
import CourseLayout from '@/components/layout/CourseLayout'
import Navbar from '@/components/layout/practice-test/Navbar'

const Layout = ({ children }: ChildrenType) => {
  // Vars
  const direction = 'ltr'

  return (
    <Providers direction={direction}>
      <LayoutWrapper verticalLayout={<CourseLayout navbar={<Navbar />}>{children}</CourseLayout>} />
    </Providers>
  )
}

export default Layout
