// Layout Imports
import type { ReactNode } from 'react'

import LayoutWrapper from '@/components/layout/LayoutWrapper'

// Component Imports
import Providers from '@/components/Providers'
import Navbar from '@/components/layout/course/Navbar'
import CourseLayout from '@/components/layout/CourseLayout'

const Layout = ({ children, courseName }: { children: ReactNode; courseName?: string }) => {
  // Vars
  const direction = 'ltr'

  return (
    <Providers direction={direction}>
      <LayoutWrapper
        verticalLayout={
          <CourseLayout
            navbar={
              <Navbar
                contentBack='Back to courses'
                name={courseName || 'Practice Exams | AWS Certified Solutions Architect Associate'}
              />
            }
          >
            {children}
          </CourseLayout>
        }
      />
    </Providers>
  )
}

export default Layout
