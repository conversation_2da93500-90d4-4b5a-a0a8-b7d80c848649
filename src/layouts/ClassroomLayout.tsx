// Layout Imports
import LayoutWrapper from '@/components/layout/LayoutWrapper'

// Component Imports
import Providers from '@/components/Providers'
import Navbar from '@/components/layout/course/Navbar'
import CourseLayout from '@/components/layout/CourseLayout'

interface Props {
  children: React.ReactNode
  name: string
}

const Layout: React.FC<Props> = ({ children, name }) => {
  // Vars
  const direction = 'ltr'

  return (
    <Providers direction={direction}>
      <LayoutWrapper
        verticalLayout={
          <CourseLayout navbar={<Navbar contentBack='Back to class' name={name} linkBack='/class-room' />}>
            {children}
          </CourseLayout>
        }
      />
    </Providers>
  )
}

export default Layout
