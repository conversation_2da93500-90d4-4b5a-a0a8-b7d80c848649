// Type Imports
import type { ChildrenType } from '@/core/types'

// Layout Imports
import LayoutWrapper from '@/components/layout/LayoutWrapper'

// Component Imports
import Providers from '@/components/Providers'
import Navigation from '@/components/layout/vertical/Navigation'
import Navbar from '@/components/layout/vertical/Navbar'
import VerticalLayout from '@/components/layout/VerticalLayout'

const Layout = ({ children }: ChildrenType) => {
  // Vars
  const direction = 'ltr'

  return (
    <Providers direction={direction}>
      <LayoutWrapper
        verticalLayout={
          <VerticalLayout
            navigation={<Navigation />}
            navbar={<Navbar name='<PERSON>' email='<EMAIL>' avatar='images/avatars/1.png' />}
          >
            {children}
          </VerticalLayout>
        }
      />
    </Providers>
  )
}

export default Layout
