type IUser = {
  name: string
  avatar: string
  email: string
}

type Answer = {
  id: string
  content: string
  correct: boolean
}

type Question = {
  id: string
  title: string
  type: string
  answers: Answer[]
  explanation: string
}

type QuestionFormRef = {
  submitForm: () => void
}

type User = {
  name: string
  email: string
  id: string
  avatar: string
  class: string
}

type DialogContentType = {
  dialogActions: string
  dialogTitle: string
  dialogContent: React.ReactNode
  handleDialogAction?: () => void
  loading?: boolean
}

type MenuState = {
  anchorEl: HTMLElement | null
  id: number | null | string
}

type SortType = 'string' | 'number' | 'date'

type TableColumn = {
  id: string
  label: string
  width?: string
  fixed: boolean
  sortable?: boolean
  sortType?: SortType
}

type SortConfig = {
  key: string
  direction: 'asc' | 'desc'
}

export type {
  IUser,
  Answer,
  Question,
  QuestionFormRef,
  User,
  DialogContentType,
  MenuState,
  SortType,
  TableColumn,
  SortConfig
}
