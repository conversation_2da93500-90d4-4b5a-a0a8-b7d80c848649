export interface Course {
  courseId?: number
  coverImage?: string | null
  courseName: string
  headline?: string | null
  originalPrice?: string | null
  salePrice?: string | null
  publishDate?: string | null
  isPublished?: boolean | null
  shortName: string
  slug: string
  examLanguage?: string | null
  accessDuration?: number | null
  explainLanguage?: string | null
  target?: string | null
  requirement?: string | null
  description?: string | null
}
