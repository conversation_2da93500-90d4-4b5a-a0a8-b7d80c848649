import type { Question } from '@/types'

export interface Exam {
  examId: number
  courseId: number
  currentVersionId: number
  examName: string
  questionCount: number
  durationMinutes: number
  passThreshold: number
  description: string
  isRandomQuestion: boolean
  publishDate: string
  createdAt: string
  updatedAt: string
}

export interface ExamVersion {
  examVersionId: number
  examId: number
  description: string
  createdAt: string
  isPublished: boolean
  versionNumber: number
}

export interface PracticeTest {
  examVersionId: number
  examId: number
  description: string
  createdAt: string
  isPublished: boolean
  versionNumber: number
  questions: Question[]
}
