export interface Question {
  questionId: number
  examVersionId: number
  questionText: string
  explanation: string
  pos: number
  answers: string[]
  correctAnswer: string[]
  createdAt: string
}

export interface NewQuestion {
  questionId: number
  examVersionId: number
  questionText: string
  explanation: string
  pos: number
  answers: Answer[]
  correctAnswer: number[]
  createdAt: string
}

export interface Answer {
  id: number
  content: string
}
