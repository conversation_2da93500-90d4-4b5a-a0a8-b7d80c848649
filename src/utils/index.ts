export const formatNumber = (number: number, separator?: string) => {
  if (isNaN(number) || number === undefined || number === null) {
    return ''
  }

  return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, () => separator ?? ',')
}

export const unformedNumber = (formattedValue: string) => {
  return formattedValue.replace(/[.,]/g, '')
}

export const getRandomId = (): number => {
  return Math.floor(Math.random() * 1000)
}

export const stripHtml = (htmlString: string) => {
  return htmlString.replace(/<[^>]+>/g, '')
}

export const convertToSlug = (text: string, domain: string) => {
  if (!text) return ''

  return (
    domain +
    text
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]+/g, '')
      .replace(/\-\-+/g, '-')
      .replace(/^-+/, '')
      .replace(/-+$/, '')
  )
}

export const convertS3ToCloudFrontUrl = (s3Url: string): string => {
  const cloudFrontDomain = 'https://static.dev.cloudmentor.pro'
  const s3BucketName = 'd-s3-cmp-cloud-exam-pro-assets.s3.ap-southeast-1.amazonaws.com'

  return s3Url.replace(`https://${s3BucketName}`, cloudFrontDomain)
}
