import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

import api from '@/api/axios'

// Type for request options
interface RequestOptions {
  headers?: Record<string, string>
  params?: Record<string, any>
}

export interface PaginationParams {
  [key: string]: any
}

// ===== GET Hook with Standardized Pagination =====
export function useApiGet<TData = unknown>(
  endpoint: string,
  queryKey: string | string[],
  params?: PaginationParams,
  options?: RequestOptions,
  enabled = true
) {
  // Create a consistent query key format that includes the params
  const fullQueryKey = Array.isArray(queryKey)
    ? params
      ? [...queryKey, params]
      : queryKey
    : params
      ? [queryKey, params]
      : [queryKey]

  return useQuery({
    queryKey: fullQueryKey,
    queryFn: async () => {
      const response = await api.get<TData>(endpoint, {
        headers: options?.headers,
        params: params
      })

      return response.data
    },
    enabled
  })
}

// ===== POST Hook =====
export function useApiPost<TData = unknown, TVariables = unknown>() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      endpoint,
      data,
      options
    }: {
      endpoint: string
      data: TVariables
      options?: RequestOptions
    }) => {
      const response = await api.post<TData>(endpoint, data, {
        headers: options?.headers,
        params: options?.params
      })

      return response
    },
    onSuccess: () => {
      // Optionally invalidate queries that might be affected by this mutation
      // Customize based on your API structure
      queryClient.invalidateQueries()
    },
    onError: () => {}
  })
}

// ===== PUT Hook =====
export function useApiPut<TData = unknown, TVariables = unknown>() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      endpoint,
      data,
      options
    }: {
      endpoint: string
      data: TVariables
      options?: RequestOptions
    }) => {
      const response = await api.put<TData>(endpoint, data, {
        headers: options?.headers,
        params: options?.params
      })

      return response
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries()
    }
  })
}

// ===== DELETE Hook =====
export function useApiDelete<TData = unknown>() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ endpoint, options }: { endpoint: string; options?: RequestOptions }) => {
      const response = await api.delete<TData>(endpoint, {
        headers: options?.headers,
        params: options?.params
      })

      return response.data
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries()
    },
    onError: () => {}
  })
}
