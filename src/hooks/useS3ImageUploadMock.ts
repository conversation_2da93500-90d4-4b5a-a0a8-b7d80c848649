// Mock implementation of useS3ImageUpload for demo purposes

export const useS3ImageUpload = () => {
  const uploadImage = async (file: File): Promise<string> => {
    // Validate file
    const maxSize = 5 * 1024 * 1024 // 5MB
    const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/webp']

    if (file.size > maxSize) {
      throw new Error('File too large. Maximum size is 5MB')
    }

    if (!allowedTypes.includes(file.type)) {
      throw new Error('Invalid file type. Only PNG, JPG, JPEG, GIF, and WebP are allowed')
    }

    // Simulate upload delay
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Return a mock image URL (using a placeholder service)
    return `https://picsum.photos/400/300?random=${Date.now()}`
  }

  return { uploadImage, isLoading: false }
}
