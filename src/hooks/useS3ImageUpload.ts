import { useApiPost } from '@/hooks/useApi'
import { convertS3ToCloudFrontUrl } from '@/utils'

interface PresignedUrlResponse {
  url: string
  fields?: Record<string, string>
}

export const useS3ImageUpload = () => {
  const getPresignedUrl = useApiPost<PresignedUrlResponse>()

  const uploadImage = async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      // Validate file
      const maxSize = 5 * 1024 * 1024 // 5MB
      const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/webp']

      if (file.size > maxSize) {
        reject(new Error('File too large. Maximum size is 5MB'))

        return
      }

      if (!allowedTypes.includes(file.type)) {
        reject(new Error('Invalid file type. Only PNG, JPG, JPEG, GIF, and WebP are allowed'))

        return
      }

      // Generate unique filename
      const timestamp = Date.now()
      const randomString = Math.random().toString(36).substring(2, 10)
      const safeFilename = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
      const uniquePath = `editor-images/${timestamp}-${randomString}-${safeFilename}`

      // Get presigned URL and upload
      getPresignedUrl.mutate(
        {
          endpoint: '/files/presigned-url',
          data: {
            path: uniquePath,
            contentType: file.type
          }
        },
        {
          onSuccess: async (res: any) => {
            try {
              const xhr = new XMLHttpRequest()

              xhr.open('PUT', res.data.url, true)
              xhr.setRequestHeader('Content-Type', file.type)

              xhr.onload = () => {
                if (xhr.status === 200) {
                  let finalUrl = res.data.url

                  try {
                    const urlObj = new URL(finalUrl)

                    finalUrl = `${urlObj.origin}${urlObj.pathname}`
                  } catch (e) {
                    console.warn('Could not parse URL, using full URL')
                  }

                  const cloudFrontUrl = convertS3ToCloudFrontUrl(finalUrl)

                  resolve(cloudFrontUrl)
                } else {
                  reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`))
                }
              }

              xhr.onerror = () => {
                reject(new Error('Upload failed due to network error'))
              }

              xhr.send(file)
            } catch (uploadError) {
              reject(new Error('Upload failed. Please try again later.'))
            }
          },
          onError: (error: any) => {
            reject(new Error(error?.response?.data?.message || 'Failed to get upload URL'))
          }
        }
      )
    })
  }

  return { uploadImage, isLoading: getPresignedUrl.isLoading }
}
