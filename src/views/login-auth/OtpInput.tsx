import type { ChangeEvent, KeyboardEvent, RefObject } from 'react'
import { useState, useRef, useEffect, forwardRef, createRef } from 'react'

import { Typography } from '@mui/material'

interface OTPInputProps {
  value?: string
  onChange?: (value: string) => void
  onComplete?: (value: string) => void
  error?: boolean
  helperText?: string
}

const OTPInput = forwardRef<HTMLDivElement, OTPInputProps>(
  ({ value = '', onChange, onComplete, error, helperText }, ref) => {
    const [values, setValues] = useState<string[]>(Array(8).fill(''))

    const inputRefs = useRef<Array<RefObject<HTMLInputElement>>>([])

    useEffect(() => {
      inputRefs.current = Array(8)
        .fill(null)
        .map(() => createRef<HTMLInputElement>())
    }, [])

    useEffect(() => {
      if (value) {
        const digits = value.split('').slice(0, 8)

        setValues(digits.concat(Array(8 - digits.length).fill('')))
      }
    }, [value])

    const updateValue = (newValues: string[]) => {
      const otp = newValues.join('')

      setValues(newValues)
      onChange?.(otp)

      if (otp.length === 8) {
        onComplete?.(otp)
      }
    }

    const handleChange = (index: number, event: ChangeEvent<HTMLInputElement>): void => {
      const inputValue = event.target.value

      if (!/^\d*$/.test(inputValue)) return

      if (inputValue.length > 1) {
        const digits = inputValue.split('').slice(0, 8)
        const newValues = [...values]

        digits.forEach((digit, idx) => {
          if (idx < 8) {
            newValues[idx] = digit
          }
        })

        updateValue(newValues)

        const lastFilledIndex = Math.min(digits.length - 1, 7)

        inputRefs.current[lastFilledIndex]?.current?.focus()
      } else {
        const newValues = [...values]

        newValues[index] = inputValue
        updateValue(newValues)

        if (inputValue && index < 7) {
          inputRefs.current[index + 1]?.current?.focus()
        }
      }
    }

    const handleKeyDown = (index: number, event: KeyboardEvent<HTMLInputElement>): void => {
      if (event.key === 'Backspace' && !values[index] && index > 0) {
        const previousInput = inputRefs.current[index - 1]?.current

        if (previousInput) {
          previousInput.focus()
          const length = previousInput.value.length

          previousInput.setSelectionRange(length, length)
        }
      }
    }

    const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>): void => {
      event.preventDefault()
      const pastedData = event.clipboardData.getData('text')
      const digits = pastedData.match(/\d/g) || []

      const newValues = [...values]

      digits.forEach((digit, idx) => {
        if (idx < 8) {
          newValues[idx] = digit
        }
      })

      updateValue(newValues)

      const lastFilledIndex = Math.min(digits.length - 1, 7)

      inputRefs.current[lastFilledIndex]?.current?.focus()
    }

    return (
      <div ref={ref}>
        <div className='flex gap-2 justify-center items-center pt-4'>
          {values.map((value, index) => (
            <input
              key={index}
              ref={inputRefs.current[index]}
              type='text'
              inputMode='numeric'
              pattern='\d*'
              maxLength={8}
              value={value}
              onChange={e => handleChange(index, e)}
              onKeyDown={e => handleKeyDown(index, e)}
              onPaste={handlePaste}
              className={`w-10 h-12 text-center bg-white border rounded-lg text-xl font-semibold transition-colors duration-200
              ${error ? 'border-red-500' : value ? 'border-primary' : 'border-secondary focus:border-primary'} `}
              aria-label={`Digit ${index + 1}`}
            />
          ))}
        </div>
        {error && helperText && (
          <Typography color='error' variant='body1' className='mt-2'>
            {helperText}
          </Typography>
        )}
      </div>
    )
  }
)

OTPInput.displayName = 'OTPInput'

export default OTPInput
