import { useState, useEffect } from 'react'

// MUI Imports
import {
  Autocomplete,
  Box,
  Button,
  Card,
  CardContent,
  FormControl,
  TextField,
  Typography,
  CircularProgress
} from '@mui/material'

// Component Imports
import ExamCompletedTable from '@/views/class-room/ExamCompletedTable'
import CardInfo from '@/views/class-room/CardInfo'
import { useApiGet } from '@/hooks/useApi'
import type { AttemptDetail } from '@/api/attempt'

interface Props {
  classId: number | null
}

interface CourseAndExam {
  courseId: number
  courseName: string
  shortName: string
  exams: {
    examId: number
    examName: string
    currentVersionId: number
  }[]
}

interface Response {
  users: AttemptDetail[]
  notAttemptedUsers: AttemptDetail[]
  total: {
    passUser: number
    failedUser: number
    notAttemptedCount: number
    totalUserParticipants: number
  }
}

const PracticeTestClassRoom: React.FC<Props> = ({ classId }) => {
  const { data: courseAndExamData, isLoading } = useApiGet<CourseAndExam[]>(
    `/classes/${classId}/courses-exams`,
    'course-exam' + classId
  )

  const [selectedCourse, setSelectedCourse] = useState<string | null>(null)
  const [selectedExam, setSelectedExam] = useState<{ label: string; value: number } | null>(null)
  const [selectedId, setSelectedId] = useState<number | null>(null)
  const [isSearching, setIsSearching] = useState(false)

  const [availableExams, setAvailableExams] = useState<
    { examId: number; examName: string; currentVersionId: number }[]
  >([])

  const {
    data: attemptsData,
    isLoading: attemptsLoading,
    refetch: refetchAttempts
  } = useApiGet<Response>(
    `/classes/${classId}/${selectedId}`, // Only fetch when selectedId exists
    ['attempts' + selectedId + classId] // Add proper query key
  )

  // Update available exams when course selection changes

  const handleSearch = async () => {
    if (!selectedExam?.value || !classId) {
      return
    }

    setIsSearching(true)

    try {
      setSelectedId(selectedExam.value)
      await refetchAttempts()
    } catch (error) {
      console.error('Failed to fetch attempts:', error)

      // Add your error handling here (toast, alert, etc.)
    } finally {
      setIsSearching(false)
    }
  }

  useEffect(() => {
    if (courseAndExamData && selectedCourse) {
      const course = courseAndExamData.find(c => c.courseName === selectedCourse || c.shortName === selectedCourse)

      if (course) {
        setAvailableExams(course.exams)
        setSelectedExam(null) // Reset exam selection when course changes
      } else {
        setAvailableExams([])
      }
    } else {
      setAvailableExams([])
    }
  }, [selectedCourse, courseAndExamData])

  return (
    <>
      <Card className='max-w-full m-auto'>
        <CardContent>
          <Box className='flex items-center gap-5 min-w-full !pt-0'>
            <Box className='flex items-center justify-between gap-5 w-full'>
              <FormControl fullWidth className='w-[30%]'>
                <Autocomplete
                  renderInput={params => <TextField {...params} size='small' label='Course Short Name' />}
                  onChange={(event, newValue) => setSelectedCourse(newValue)}
                  clearOnEscape
                  options={courseAndExamData?.map(c => c.courseName) || []}
                  value={selectedCourse}
                  loading={isLoading}
                  loadingText='Loading courses...'
                  isOptionEqualToValue={(option, value) => option === value}
                />
              </FormControl>
              <FormControl fullWidth size='small'>
                <Autocomplete
                  renderInput={params => <TextField {...params} size='small' label='Practice Test' />}
                  clearOnEscape
                  options={availableExams.map(exam => ({ label: exam.examName, value: exam.examId })) || []}
                  value={selectedExam}
                  getOptionLabel={option => option.label}
                  onChange={(event, newValue) => setSelectedExam(newValue)}
                  disabled={!selectedCourse || availableExams.length === 0}
                  loading={(selectedCourse && availableExams.length === 0) || false}
                  loadingText='Loading exams...'
                  isOptionEqualToValue={(option, value) => option === value}
                />
              </FormControl>
            </Box>

            <Button
              variant='contained'
              className='w-32'
              startIcon={
                isSearching ? <CircularProgress size={20} color='inherit' /> : <i className='ri-search-line' />
              }
              onClick={handleSearch}
              disabled={!selectedCourse || !selectedExam || isSearching || attemptsLoading}
            >
              {isSearching ? 'Searching...' : 'Search'}
            </Button>
          </Box>
        </CardContent>
      </Card>
      <Box className='flex justify-between mt-5 gap-20'>
        <CardInfo
          title='Practice Test Participants'
          content={
            attemptsData
              ? `${attemptsData.users.length}/${
                  attemptsData.total?.notAttemptedCount + attemptsData.total?.totalUserParticipants
                }`
              : '-'
          }
        />
        <CardInfo title='Passed Users' content={attemptsData?.total?.passUser?.toString() || '-'} />
        <CardInfo title='Failed Users' content={attemptsData?.total?.failedUser?.toString() || '-'} />
      </Box>
      <Card className='max-w-full m-auto mt-5'>
        <CardContent>
          <Box className='flex flex-col gap-9'>
            <Box>
              <Typography variant='h6' className='mbe-4 font-semibold'>
                Exam Completers
              </Typography>
              <ExamCompletedTable rows={attemptsData?.users || []} type='completed' />
            </Box>
            <Box>
              <Typography variant='h6' className='mbe-4 font-semibold'>
                Pending Examinees
              </Typography>
              <ExamCompletedTable rows={attemptsData?.notAttemptedUsers || []} type='pending' />
            </Box>
            <Box>
              <Button variant='contained' size='small' startIcon={<i className='ri-download-2-fill'></i>}>
                Download
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </>
  )
}

export default PracticeTestClassRoom
