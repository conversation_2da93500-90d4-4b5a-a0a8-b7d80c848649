import Image from 'next/image'

import { <PERSON>, <PERSON><PERSON>, Card, CardContent, Typography } from '@mui/material'

interface Props {
  firstName: string
  lastName: string
  email: string
  isClass?: boolean
  userId: number
  changeClass: () => void
}

const UserCard: React.FC<Props> = ({ email, isClass, changeClass, firstName, lastName }) => {
  return (
    <Card className='w-[450px]' variant='elevation'>
      <CardContent className='!py-3 pbe-0'>
        <Box className='flex items-center justify-between'>
          <Box className='flex items-center gap-4'>
            <Image src='/images/avatars/3.png' alt='user-card' width={57} height={57} className='rounded-lg' />
            <Box className='flex flex-col gap-2'>
              <Typography>{firstName + ' ' + lastName}</Typography>
              <Typography variant='body2'>{email}</Typography>
            </Box>
          </Box>
          <Button
            variant={isClass ? 'outlined' : 'contained'}
            color={isClass ? 'secondary' : 'primary'}
            onClick={changeClass}
          >
            {isClass ? 'Remove' : 'Add'}
          </Button>
        </Box>
      </CardContent>
    </Card>
  )
}

export default UserCard
