'use client'

// React Imports
import { useEffect } from 'react'

// MUI Imports
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'

import * as yup from 'yup'

import { yupResolver } from '@hookform/resolvers/yup'

import { Controller, useForm } from 'react-hook-form'

import { useApiGet, useApiPut } from '@/hooks/useApi'
import type { Course } from '@/api/courses'
import type { Class } from '@/api/class'
import { useToast } from '@/contexts/ToastContext'

interface Props {
  title: string
  initialData: FormData
  loading: boolean
  buttonText: string
  id: number | null
}

const ITEM_HEIGHT = 48
const ITEM_PADDING_TOP = 8

const MenuProps = {
  PaperProps: {
    style: {
      width: 250,
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP
    }
  }
}

interface PaginatedResponse<T> {
  courses: T[]
  meta: {
    totalCount: number
    page: number
    limit: number
  }
}

interface FormData {
  className: string
  courseIds: number[]
}

const schema = yup.object({
  className: yup.string().trim().required('Class name is required'),
  courseIds: yup.array().min(1, 'At least one course is required').required('Course is required')
})

const resolver = yupResolver(schema)

const ClassroomInfoForm: React.FC<Props> = ({ title, initialData, buttonText, loading, id }) => {
  const { showToast } = useToast()

  const { data: courseData } = useApiGet<PaginatedResponse<Course>>('/courses', 'courses', {
    limit: 100,
    page: 1
  }) as { data: PaginatedResponse<Course> }

  const updateClassInfo = useApiPut<Class, FormData>()

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    control
  } = useForm<FormData>({
    resolver: resolver,
    defaultValues: initialData
  })

  const onSubmit = async (data: FormData) => {
    if (!id) {
      return
    }

    await updateClassInfo.mutate(
      {
        endpoint: `/classes/${id}`,
        data: data
      },
      {
        onSuccess: () => {
          showToast('Class info updated successfully')
        },
        onError: (res: any) => {
          showToast(res.response.data.message, 'error')
        }
      }
    )
  }

  useEffect(() => {
    reset(initialData)
  }, [initialData, reset])

  return (
    <Card className='max-w-full m-auto'>
      <CardHeader title={title} />
      {loading ? (
        <Box className='w-full flex justify-center min-h-32'>
          <CircularProgress />
        </Box>
      ) : (
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={5}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label='Class Name'
                  placeholder='Practice Exams | AWS Certified Solutions Architect Associate'
                  size='small'
                  error={!!errors.className}
                  helperText={errors.className?.message}
                  {...register('className')}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth className='min-w-[560px]'>
                  <InputLabel size='small' id='short-name-label'>
                    Course Short Name
                  </InputLabel>
                  <Controller
                    control={control}
                    name='courseIds'
                    render={({ field }) => (
                      <Select
                        {...field}
                        size='small'
                        labelId='short-name-label'
                        id='short-name-select'
                        multiple
                        label='Course Short Name'
                        MenuProps={MenuProps}
                      >
                        {courseData?.courses.map(course => (
                          <MenuItem key={course.courseId} value={course.courseId}>
                            {course.shortName}
                          </MenuItem>
                        ))}
                      </Select>
                    )}
                  />
                </FormControl>
                {errors.courseIds && (
                  <Typography className='text-error text-[13px] mt-1 ml-[14px]'>{errors.courseIds.message}</Typography>
                )}
              </Grid>
              <Grid item xs={12} className='pt-7'>
                <Button variant='contained' type='submit'>
                  {buttonText}
                </Button>
              </Grid>
            </Grid>
          </form>
        </CardContent>
      )}
    </Card>
  )
}

export default ClassroomInfoForm
