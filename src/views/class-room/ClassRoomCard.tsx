import React from 'react'

import { <PERSON>, Card, CardContent, IconButton, Typography } from '@mui/material'

interface Props {
  className: string
  active: boolean
  classId: number
  handleEditClassroom: () => void
  handleArchive: (classId: number) => void
  handleDelete: (classId: number) => void
  courseShortNames: string[]
}

const ClassRoomCard: React.FC<Props> = ({
  className,
  active,
  handleEditClassroom,
  handleArchive,
  handleDelete,
  courseShortNames,
  classId
}) => {
  return (
    <Card className='h-full w-[400px]' variant={active ? 'outlined' : 'elevation'}>
      <CardContent className='flex flex-col items-center text-center'>
        <Box className='flex items-center justify-between w-full'>
          <Box>
            <Typography variant='h5' className='mbe-2' textAlign='left'>
              {className}
            </Typography>

            <Typography variant='h6' className='mbe-2' textAlign='left' color={active ? 'primary' : 'text.secondary'}>
              {courseShortNames.join(', ')}
            </Typography>
          </Box>
          <Box>
            <IconButton color={active ? 'primary' : 'secondary'} className='p-1' onClick={handleEditClassroom}>
              <i className='ri-pencil-fill'></i>
            </IconButton>
            <IconButton color={active ? 'primary' : 'secondary'} className='p-1' onClick={() => handleArchive(classId)}>
              {active ? <i className='ri-inbox-archive-fill'></i> : <i className='ri-inbox-unarchive-fill'></i>}
            </IconButton>
            <IconButton color={active ? 'primary' : 'secondary'} className='p-1' onClick={() => handleDelete(classId)}>
              <i className='ri-delete-bin-6-fill'></i>
            </IconButton>
          </Box>
        </Box>
      </CardContent>
    </Card>
  )
}

export default ClassRoomCard
