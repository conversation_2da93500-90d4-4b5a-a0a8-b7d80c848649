import { Card, CardContent, Typography } from '@mui/material'

interface Props {
  title: string
  content: string | number
}

const CardInfo: React.FC<Props> = ({ title, content }) => {
  return (
    <Card className='flex-1'>
      <CardContent>
        <Typography variant='body2' className='mbe-2'>
          {title}
        </Typography>
        <Typography variant='h6'>{content && content}</Typography>
      </CardContent>
    </Card>
  )
}

export default CardInfo
