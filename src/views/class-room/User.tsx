import { useEffect, useState } from 'react'

import { <PERSON><PERSON>, Card, CardContent, CardHeader, Grid, TextField, Typo<PERSON> } from '@mui/material'

import UserCard from '@/views/class-room/UserCard'
import useUserStore from '@/store/userStore'
import DialogCustom from '@/views/Dialog'
import { useApiGet, useApiPut } from '@/hooks/useApi'
import type { User } from '@/api/user'
import { useDebounce } from '@/hooks/useDebounce'
import { useToast } from '@/contexts/ToastContext'

// MUI Imports

interface Props {
  title: string
  classId: number | null
}

interface PaginatedResponse<T> {
  users: T[]
  meta: {
    totalCount: number
    page: number
    limit: number
  }
}

const UserForm: React.FC<Props> = ({ title, classId }) => {
  const { showToast } = useToast()

  const [searchQuery, setSearchQuery] = useState<string>('')
  const debouncedSearchQuery = useDebounce(searchQuery, 500)
  const [userList, setUserList] = useState<User[]>([])

  const { data: allUserData } = useApiGet<PaginatedResponse<User>>(
    searchQuery.trim().length !== 0 ? `/users` : '',
    'user-in-class',
    { page: 1, limit: 100, email: debouncedSearchQuery }
  )

  const { data } = useApiGet<PaginatedResponse<User>>(`/classes/${classId}/users`, 'user-class' + classId, {
    page: 1,
    limit: 100
  })

  const updateUserClass = useApiPut<number[]>()

  const { setUsers, hasChanges, addUser, removeUser } = useUserStore()

  const [openDialog, setOpenDialog] = useState<boolean>(false)
  const [idSelectedUser, setIdSelectedUser] = useState<number | null>(null)

  const handleAddUser = (newUser: User) => {
    // Check if user already exists in the list
    if (userList.some(u => u.userId === newUser.userId)) {
      showToast('User already exists in the class', 'warning')

      return
    } else {
      // Add user to the local state
      setUserList(prevList => [...prevList, newUser])

      // Add user to the store
      addUser(newUser)

      showToast('User added successfully', 'success')
    }
  }

  const handleRemoveUser = (userId: number) => {
    setIdSelectedUser(userId)
    setOpenDialog(true)
  }

  const confirmRemoveUser = () => {
    if (idSelectedUser) {
      // Remove user from the local state
      setUserList(prevList => prevList.filter(user => user.userId !== idSelectedUser))

      // Remove user from the store
      removeUser(idSelectedUser)

      // Close the dialog
      setOpenDialog(false)
    }
  }

  const handleSave = async () => {
    const listIdUser = userList.map(u => u.userId)

    await updateUserClass.mutate(
      {
        endpoint: `/classes/${classId}/users`,
        data: { userIds: listIdUser }
      },
      {
        onSuccess: () => {
          showToast('User list updated successfully', 'success')
        },
        onError: (error: any) => {
          showToast(error.response?.data?.message as string, 'error')
        }
      }
    )
  }

  useEffect(() => {
    if (data?.users) {
      setUserList(data.users)

      // Update the store with initial users
      setUsers(data.users)
    }
  }, [data?.users, setUsers])

  return (
    <>
      <Card className='max-w-full m-auto'>
        <CardHeader title={title} />
        <CardContent>
          <Grid container spacing={3} className='w-full'>
            <Grid item xs={12} md={6} className='flex items-center gap-5 min-w-full !pt-0'>
              <TextField
                fullWidth
                size='small'
                placeholder='Enter user email'
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
              />
              <Button variant='contained' className='w-32' startIcon={<i className='ri-search-line' />}>
                Search
              </Button>
            </Grid>
            <Grid item xs={12} md={6} className='min-w-full'>
              <Typography variant='h6'>Search Result</Typography>
            </Grid>
            <Grid item xs={12} md={6} className='min-w-full'>
              <Grid container className='gap-5'>
                {allUserData?.users.map(item => (
                  <UserCard key={item.userId} {...item} isClass={false} changeClass={() => handleAddUser(item)} />
                ))}
              </Grid>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      <Card className='max-w-full m-auto mt-5'>
        <CardContent>
          <Grid container spacing={3} className='w-full'>
            <Grid item xs={12} md={6} className='min-w-full'>
              <Typography variant='h6'>User in Class</Typography>
            </Grid>
            <Grid item xs={12} md={6} className='min-w-full'>
              <Grid container className='gap-5'>
                {userList.map(item => (
                  <UserCard
                    key={item.userId}
                    {...item}
                    isClass={true}
                    changeClass={() => handleRemoveUser(item.userId)}
                  />
                ))}
              </Grid>
            </Grid>
            <Grid item xs={12} md={6} className='min-w-full'>
              <Button variant='contained' color='primary' disabled={!hasChanges} onClick={handleSave}>
                Save
              </Button>
            </Grid>
          </Grid>
        </CardContent>
        <DialogCustom
          dialogTitle='Remove User'
          dialogContent={<Typography>Are you sure you want to remove this User?</Typography>}
          dialogActions={'Delete'}
          handleDialogAction={confirmRemoveUser}
          openDialog={openDialog}
          setOpenDialog={setOpenDialog}
        />
      </Card>
    </>
  )
}

export default UserForm
