import React, { useState } from 'react'

import {
  Box,
  Collapse,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Paper,
  TableFooter,
  TablePagination,
  TableSortLabel
} from '@mui/material'

import type { AttemptDetail } from '@/api/attempt'

interface ExamCompletedTableProps {
  rows: AttemptDetail[]
  type: string
}

const Row: React.FC<{ row: AttemptDetail; type: string }> = ({ row, type }) => {
  const [open, setOpen] = useState(false)

  return (
    <>
      <TableRow sx={{ '& > *': { borderBottom: 'unset' } }}>
        <TableCell component='th' scope='row'>
          {row.firstName} {row.lastName}
        </TableCell>
        <TableCell>{row.attempts ? row.attempts[0].score : '-'}</TableCell>
        <TableCell>
          {type === 'completed' ? (
            <Typography
              variant='body2'
              className={row.attempts && row.attempts[0].isPassed ? 'text-primary' : 'text-error'}
            >
              {row.attempts ? (row.attempts[0].isPassed ? 'Passed' : 'Failed') : '-'}
            </Typography>
          ) : (
            '-'
          )}
        </TableCell>
        <TableCell>{row.attempts ? row.attempts[0].examVersionId : '-'}</TableCell>
        <TableCell>
          {row.attempts && row.attempts.slice(1)?.length > 0 && (
            <IconButton aria-label='expand row' size='small' onClick={() => setOpen(!open)}>
              {open ? <i className='ri-arrow-up-s-line' /> : <i className='ri-arrow-down-s-line' />}
            </IconButton>
          )}
        </TableCell>
      </TableRow>
      <TableRow>
        {row.attempts && row.attempts.slice(1).length > 0 && (
          <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={5}>
            <Collapse in={open} timeout='auto' unmountOnExit>
              <Box sx={{ margin: 1 }}>
                <Typography variant='h6' gutterBottom component='div'>
                  History
                </Typography>
                <Table size='small' aria-label='purchases'>
                  <TableHead>
                    <TableRow>
                      <TableCell>Date</TableCell>
                      <TableCell>Score</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Time Spent(m)</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {row.attempts &&
                      row.attempts.slice(1).map(historyRow => (
                        <TableRow key={historyRow.createdAt}>
                          <TableCell component='th' scope='row'>
                            {new Date(historyRow.createdAt).toLocaleDateString('en-GB', {
                              day: '2-digit',
                              month: '2-digit',
                              year: 'numeric'
                            })}
                          </TableCell>
                          <TableCell>{historyRow.score}</TableCell>
                          <TableCell>{historyRow.isPassed ? 'Passed' : 'Failed'}</TableCell>
                          <TableCell>{historyRow.timeSpentMinutes}</TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </Box>
            </Collapse>
          </TableCell>
        )}
      </TableRow>
    </>
  )
}

const ExamCompletedTable: React.FC<ExamCompletedTableProps> = ({ rows, type }) => {
  const [page, setPage] = useState<number>(1)
  const [rowsPerPage, setRowsPerPage] = useState<number>(5)
  const [order, setOrder] = useState<'asc' | 'desc'>('asc')
  const [orderBy] = useState<keyof AttemptDetail>('score' as keyof AttemptDetail)

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage + 1)
  }

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  const sortedRows = React.useMemo(() => {
    if (type === 'pending') {
      return [...rows]
    }

    return [...rows].sort((a, b) => {
      const isAsc = order === 'asc'

      // Get scores from first attempts
      const scoreA = a.attempts[0]?.score ?? -1
      const scoreB = b.attempts[0]?.score ?? -1

      // Handle undefined or null scores
      if (scoreA === -1 && scoreB === -1) return 0
      if (scoreA === -1) return isAsc ? 1 : -1
      if (scoreB === -1) return isAsc ? -1 : 1

      // Compare scores
      return isAsc ? scoreA - scoreB : scoreB - scoreA
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rows, order, orderBy])

  const paginatedRows = sortedRows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)

  return (
    <TableContainer component={Paper}>
      <Table aria-label='collapsible table'>
        <TableHead>
          <TableRow>
            <TableCell>User Name</TableCell>
            <TableCell sortDirection={orderBy === ('score' as keyof AttemptDetail) ? order : false}>
              {type === 'completed' ? (
                <TableSortLabel
                  active={true} // Always show sort icon
                  direction={order}
                  onClick={() => {
                    setOrder(order === 'asc' ? 'desc' : 'asc')
                  }}
                  sx={{
                    visibility: 'visible',
                    display: 'flex',
                    alignItems: 'center'
                  }}
                >
                  Test Score
                </TableSortLabel>
              ) : (
                'Test Score'
              )}
            </TableCell>
            <TableCell>Result</TableCell>
            <TableCell>Attempt</TableCell>
            <TableCell />
          </TableRow>
        </TableHead>
        <TableBody>
          {paginatedRows.map(row => (
            <Row key={row.userId} row={row} type={type} />
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              count={rows.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage='Rows per page'
            />
          </TableRow>
        </TableFooter>
      </Table>
    </TableContainer>
  )
}

export default ExamCompletedTable
