// Next Imports
import Link from 'next/link'
import { useRouter } from 'next/navigation'

// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import TextField from '@mui/material/TextField'
import Button from '@mui/material/Button'

// Type Imports
import { Controller, useForm } from 'react-hook-form'

import { Box } from '@mui/material'

import type { Mode } from '@/core/types'

// Component Imports
import Logo from '@/components/layout/shared/Logo'
import Illustrations from '@/components/Illustrations'

// Hook Imports
import { useImageVariant } from '@/core/hooks/useImageVariant'

const Login = ({ mode }: { mode: Mode }) => {
  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm()

  // Vars
  const darkImg = '/images/pages/auth-v1-mask-dark.png'
  const lightImg = '/images/pages/auth-v1-mask-light.png'

  // Hooks
  const router = useRouter()
  const authBackground = useImageVariant(mode, lightImg, darkImg)

  const onSubmit = handleSubmit(data => {
    console.log(data)
    router.push('/')
  })

  return (
    <div className='flex flex-col justify-center items-center min-bs-[100dvh] relative p-6'>
      <Card className='flex flex-col sm:is-[450px]'>
        <CardContent className='p-6 sm:!p-12'>
          <Link href='/' className='flex justify-center items-center mbe-6'>
            <Logo />
          </Link>
          <div className='flex flex-col gap-5'>
            <div>
              <Typography variant='h4'>{`Welcome to Exam Admin! 👋🏻`}</Typography>
              <Typography className='mbs-1'>Please sign-in to your account and start the adventure</Typography>
            </div>
            <form noValidate autoComplete='off' onSubmit={onSubmit} className='flex flex-col gap-5'>
              <Box>
                <Controller
                  name='email'
                  control={control}
                  rules={{
                    required: 'This field is required',
                    validate: value => {
                      const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i

                      return emailRegex.test(value) || 'Invalid email address'
                    }
                  }}
                  render={({ field }) => <TextField {...field} autoFocus fullWidth required label='Email' />}
                />
                {errors?.email && (
                  <Typography variant='body2' color='error' className='mt-1'>
                    {errors?.email?.message?.toString()}
                  </Typography>
                )}
              </Box>
              <Button
                className='mt-2'
                fullWidth
                variant='contained'
                type='submit'
                size='large'
                onClick={() => router.push('/login-auth')}
              >
                Log In
              </Button>
            </form>
          </div>
        </CardContent>
      </Card>
      <Illustrations maskImg={{ src: authBackground }} />
    </div>
  )
}

export default Login
