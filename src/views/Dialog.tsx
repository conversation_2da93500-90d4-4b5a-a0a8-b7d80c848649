import React from 'react'

// MUI Imports
import { Button, Dialog, DialogActions, DialogContent, DialogTitle, IconButton } from '@mui/material'

interface Props {
  openDialog: boolean
  setOpenDialog: (value: boolean) => void
  dialogTitle: string
  dialogContent: React.ReactNode
  dialogActions: string
  handleDialogAction?: () => void
  handleClose?: () => void
  loading?: boolean
  isDisableDialogAction?: boolean
}

const DialogCustom: React.FC<Props> = ({
  openDialog,
  setOpenDialog,
  dialogTitle,
  dialogContent,
  handleDialogAction,
  dialogActions,
  handleClose,
  loading = false,
  isDisableDialogAction = false
}) => {
  return (
    <Dialog
      open={openDialog}
      onClose={() => {
        handleClose && handleClose()
        setOpenDialog(false)
      }}
      aria-labelledby='customized-dialog-title'
    >
      <DialogTitle id='customized-dialog-title'>{dialogTitle}</DialogTitle>
      <IconButton
        aria-label='close'
        onClick={() => {
          handleClose && handleClose()
          setOpenDialog(false)
        }}
        sx={theme => ({
          position: 'absolute',
          right: 8,
          top: 8,
          color: theme.palette.grey[500]
        })}
      >
        <i className='ri-close-line'></i>
      </IconButton>
      <DialogContent className='pt-1'>{dialogContent}</DialogContent>
      <DialogActions>
        <Button
          variant='outlined'
          color='secondary'
          onClick={() => {
            handleClose && handleClose()
            setOpenDialog(false)
          }}
        >
          Cancel
        </Button>
        <Button loading={loading} disabled={isDisableDialogAction} variant='contained' onClick={handleDialogAction}>
          {dialogActions}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default DialogCustom
