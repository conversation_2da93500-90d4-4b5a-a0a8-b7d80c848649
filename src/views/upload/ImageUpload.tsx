import type { ChangeEvent } from 'react'
import { useEffect, useState } from 'react'

import { Button, Typography } from '@mui/material'

import { useApiPost } from '@/hooks/useApi'
import { convertS3ToCloudFrontUrl } from '@/utils'

interface ImageUploadProps {
  defaultImage?: string
  onImageUploaded?: (imageUrl: string) => void
  maxSize?: number
  allowedFormats?: string
  onFile: string
  onReset?: () => void
}

// Add interface for the presigned URL response
interface PresignedUrlResponse {
  url: string
  fields?: Record<string, string>
}

const ImageUpload = ({
  defaultImage = '/images/avatars/1.png',
  onImageUploaded,
  maxSize = 800,
  allowedFormats = '.png, .jpg, .jpeg, .gif',
  onFile,
  onReset
}: ImageUploadProps) => {
  const [imgSrc, setImgSrc] = useState(defaultImage)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)

  const getPresignedUrl = useApiPost<PresignedUrlResponse>()

  const handleFileInputChange = async (event: ChangeEvent) => {
    const reader = new FileReader()
    const { files } = event.target as HTMLInputElement

    setError(null)

    if (files && files.length !== 0) {
      const file = files[0]

      setIsUploading(true)

      try {
        if (file.size > maxSize * 1024) {
          setError(`File too large. Maximum size is ${maxSize}KB`)
          setIsUploading(false)

          return
        }

        if (!file.type || !allowedFormats.includes(file.type)) {
          setError(`Invalid file type. Allowed formats: ${allowedFormats}`)
          setIsUploading(false)

          return
        }

        const timestamp = Date.now()
        const randomString = Math.random().toString(36).substring(2, 10)
        const safeFilename = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
        const uniquePath = `${onFile}/${timestamp}-${randomString}-${safeFilename}`

        reader.readAsDataURL(file)

        getPresignedUrl.mutate(
          {
            endpoint: '/files/presigned-url',
            data: {
              path: uniquePath,
              contentType: file.type
            }
          },
          {
            onSuccess: async (res: any) => {
              try {
                const xhr = new XMLHttpRequest()

                xhr.open('PUT', res.data.url, true)
                xhr.setRequestHeader('Content-Type', file.type)

                xhr.upload.onprogress = event => {
                  if (event.lengthComputable) {
                    const percentCompleted = Math.round((event.loaded * 100) / event.total)

                    setUploadProgress(percentCompleted)
                  }
                }

                xhr.onload = () => {
                  if (xhr.status === 200) {
                    let finalUrl = res.data.url

                    try {
                      const urlObj = new URL(finalUrl)

                      finalUrl = `${urlObj.origin}${urlObj.pathname}`
                    } catch (e) {
                      console.warn('Could not parse URL, using full URL')
                    }

                    const cloudFrontUrl = convertS3ToCloudFrontUrl(finalUrl)

                    if (onImageUploaded) {
                      onImageUploaded(cloudFrontUrl)
                      setImgSrc(cloudFrontUrl)
                    }

                    setError(null)
                  } else {
                    setError(`Upload failed: ${xhr.status} ${xhr.statusText}`)
                    setImgSrc(defaultImage)
                  }

                  setIsUploading(false)
                  setUploadProgress(0)
                }

                xhr.onerror = () => {
                  setError('Upload failed due to network error')
                  setImgSrc(defaultImage)
                  setIsUploading(false)
                  setUploadProgress(0)
                }

                xhr.send(file)
              } catch (uploadError) {
                console.error('Upload error:', uploadError)
                setError('Upload failed. Please try again later.')
                setImgSrc(defaultImage)
                setIsUploading(false)
              }
            },
            onError: (error: any) => {
              console.error('Failed to get presigned URL:', error)
              setError(error?.response?.data?.message || 'Failed to get upload URL')
              setImgSrc(defaultImage)
              setIsUploading(false)
            }
          }
        )
      } catch (err) {
        console.error('Upload failed:', err)
        setError('Upload failed. Please try again later.')
        setImgSrc(defaultImage)
        setIsUploading(false)
      }
    }
  }

  useEffect(() => {
    if (defaultImage) {
      setImgSrc(defaultImage)
    }
  }, [defaultImage])

  return (
    <div className='flex max-sm:flex-col items-center gap-6'>
      <img height={100} width={100} className='rounded' src={imgSrc} alt='Profile' />
      <div className='flex flex-grow flex-col gap-4'>
        <div className='flex flex-col sm:flex-row gap-4'>
          <Button
            component='label'
            size='small'
            variant='contained'
            htmlFor='account-settings-upload-image'
            disabled={isUploading}
          >
            {isUploading ? `Uploading ${uploadProgress}%` : 'Upload New Photo'}
            <input
              hidden
              type='file'
              accept={allowedFormats}
              onChange={handleFileInputChange}
              id='account-settings-upload-image'
              disabled={isUploading}
            />
          </Button>
          <Button
            size='small'
            variant='outlined'
            color='error'
            disabled={(imgSrc === '/images/empty.webp' && defaultImage === '') || isUploading}
            onClick={onReset}
          >
            Reset
          </Button>
        </div>
        <Typography variant='body2' className='text-error'>
          {error}
        </Typography>
      </div>
    </div>
  )
}

export default ImageUpload
