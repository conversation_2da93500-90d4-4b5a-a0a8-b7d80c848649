import React from 'react'

// MUI Imports
import { Dialog, DialogActions, DialogContent, DialogTitle, IconButton } from '@mui/material'

interface Props {
  openDialog: boolean
  setOpenDialog: (value: boolean) => void
  dialogTitle: string
  dialogContent: React.ReactNode
  dialogActions: React.ReactNode
  onClose?: () => void
}

const DialogCustom: React.FC<Props> = ({
  openDialog,
  setOpenDialog,
  dialogTitle,
  dialogContent,
  dialogActions,
  onClose
}) => {
  return (
    <Dialog open={openDialog} onClose={onClose} aria-labelledby='customized-dialog-title'>
      <DialogTitle id='customized-dialog-title'>{dialogTitle}</DialogTitle>
      <IconButton
        aria-label='close'
        onClick={() => {
          setOpenDialog(false)
          onClose && onClose()
        }}
        sx={theme => ({
          position: 'absolute',
          right: 8,
          top: 8,
          color: theme.palette.grey[500]
        })}
      >
        <i className='ri-close-line'></i>
      </IconButton>
      <DialogContent className='pt-1'>{dialogContent}</DialogContent>
      <DialogActions>{dialogActions}</DialogActions>
    </Dialog>
  )
}

export default DialogCustom
