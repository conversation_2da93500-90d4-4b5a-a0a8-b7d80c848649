'use client'
import { useState } from 'react'

// MUI Imports
import { <PERSON><PERSON>, <PERSON>, Divider, Typography } from '@mui/material'

import DialogCustom from '@/views/Dialog'
import { useApiDelete, useApiPut } from '@/hooks/useApi'
import { useToast } from '@/contexts/ToastContext'
import type { Course } from '@/api/courses'

interface Props {
  courseId: string
  isPublished: boolean
}

const Setting: React.FC<Props> = ({ courseId, isPublished }) => {
  const { showToast } = useToast()

  const unpublishCourse = useApiPut<Course>()

  const [open, setOpen] = useState<boolean>(false)
  const deleteCourse = useApiDelete<{ id: string }>()

  const handleDeleteCourse = async () => {
    if (courseId) {
      await deleteCourse.mutate(
        { endpoint: `/courses/${courseId}` },
        {
          onSuccess: () => showToast('Course deleted successfully', 'success'),
          onError: (err: any) => {
            showToast(err.response.data.message as string, 'error')
            console.log(err)
          }
        }
      )
      setOpen(false)
    }
  }

  const handleUnpublishCourse = async () => {
    await unpublishCourse.mutate(
      {
        endpoint: `/courses/${courseId}/unpublish`,
        data: {}
      },
      {
        onSuccess: () => {
          showToast('Course unpublished successfully', 'success')
        },
        onError: (error: any) => {
          showToast(error.response?.data?.message as string, 'error')
        }
      }
    )
  }

  return (
    <Card className='px-4 py-6 max-w-[68%] m-auto'>
      <Typography variant='h5' className='mb-2'>
        Setting
      </Typography>
      <Divider />
      <div className='mt-4'>
        <Typography variant='h6' className='mb-2'>
          Course Status
        </Typography>
        <Typography color='text.primary' className='mb-9'>
          This course is not published on the Cloud Exem Pro marketplace.
        </Typography>
        <div className='flex gap-3 items-center mb-4'>
          <div className='w-44'>
            <Button
              variant='outlined'
              color='secondary'
              size='small'
              className='w-44'
              fullWidth
              disabled={!isPublished}
              onClick={handleUnpublishCourse}
            >
              Unpublish
            </Button>
          </div>
          <Typography color='text.primary'>
            New students cannot find your course via search, but existing students can still access content.
          </Typography>
        </div>
        <div className='flex gap-3 items-center'>
          <div className='w-44'>
            <Button variant='outlined' color='error' size='small' fullWidth onClick={() => setOpen(true)}>
              Delete
            </Button>
          </div>
          <Typography color='text.primary'>Courses cannot be deleted after students have enrolled.</Typography>
        </div>
      </div>
      <DialogCustom
        loading={deleteCourse.isLoading}
        dialogTitle='Delete your course?'
        dialogActions='Delete'
        handleDialogAction={handleDeleteCourse}
        openDialog={open}
        setOpenDialog={setOpen}
        dialogContent={
          <Typography>Are you sure you want to delete this course? This is permanent and cannot be undone.</Typography>
        }
      />
    </Card>
  )
}

export default Setting
