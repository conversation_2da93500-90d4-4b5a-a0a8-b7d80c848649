import React, { useState } from 'react'

// Next Imports
import { useRouter } from 'next/navigation'

import { Controller, useForm } from 'react-hook-form'

// MUI Imports
import type { SelectChangeEvent } from '@mui/material'
import {
  Button,
  Card,
  CircularProgress,
  Divider,
  FormControl,
  IconButton,
  InputLabel,
  Menu,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'

// Components Imports

import Dropdown from '@/components/DropdownTableColumn'
import TableCustom from '@/components/Table'
import DialogCustom from '@/views/Dialog'
import type { Exam, ExamVersion } from '@/api/exam'
import { useApiDelete, useApiGet, useApiPost, useApiPut } from '@/hooks/useApi'
import { useDebounce } from '@/hooks/useDebounce'
import { useToast } from '@/contexts/ToastContext'

type DialogProps = {
  title: string
  content: React.ReactNode
  actions: string
  handleDialogAction?: () => void
}

interface PaginatedResponse<T> {
  exams: T[]
  meta: {
    totalCount: number
    page: number
    limit: number
  }
}

const PracticeTest: React.FC<{ courseId: string }> = ({ courseId }) => {
  const router = useRouter()

  const columns = [
    { id: 'examId', label: 'Exam Code', fixed: true },
    { id: 'examName', label: 'Exam Name', fixed: true },
    { id: 'currentVersionId', label: 'Exam Version', fixed: false },
    { id: 'questionCount', label: 'Question Count', fixed: false },
    { id: 'durationMinutes', label: 'Duration (minutes)', fixed: false },
    { id: 'passThreshold', label: 'Pass Rate', fixed: false },
    { id: 'updatedAt', label: 'Updated At', fixed: false }
  ]

  const [selectedColumns, setSelectedColumns] = useState<string[]>(columns.map(col => col.id))
  const [page, setPage] = useState<number>(1)
  const [rowsPerPage, setRowsPerPage] = useState<number>(10)
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [openDialog, setOpenDialog] = useState<boolean>(false)
  const [dialogType, setDialogType] = useState<string>('delete')
  const [practiceVersion, setPracticeVersion] = useState<string>('')
  const [examId, setExamId] = useState<string>('')
  const debouncedSearchQuery = useDebounce(searchQuery)
  const { showToast } = useToast()

  const { data: data, isLoading: loading } = useApiGet<PaginatedResponse<Exam>>(
    `courses/${courseId}/exams`,
    'exams' + courseId,
    {
      limit: rowsPerPage,
      page: page,
      q: debouncedSearchQuery
    }
  )

  const { data: examVersions, isLoading } = useApiGet<ExamVersion[]>(
    `/exams/${examId}/versions`,
    'exam-versions' + examId
  )

  const {
    handleSubmit,
    formState: { errors },
    control,
    reset
  } = useForm({
    defaultValues: {
      examName: ''
    }
  })

  const createPracticePlan = useApiPost<{ examId: number }, { examId: number }>()

  const createExam = useApiPost<Exam, { examName: string }>()

  const deleteExam = useApiDelete<Exam>()

  const updateExam = useApiPut<{ examId: number }, { currentVersionId: number }>()

  const handleColumnToggle = (columnId: string) => {
    setSelectedColumns(prev => (prev.includes(columnId) ? prev.filter(id => id !== columnId) : [...prev, columnId]))
  }

  const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    setPage(newPage + 1)
  }

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  const onAddNew = () => {
    handleClose()
    setDialogType('addNew')
    setOpenDialog(true)
  }

  const renderCell = (columnId: string, rowData: any) => {
    if (columnId === 'examName') {
      return <span className='whitespace-nowrap'>{rowData[columnId]}</span>
    }

    return rowData[columnId]
  }

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)

  const handleClose = () => {
    setAnchorEl(null)
    reset()
    setPracticeVersion('')
  }

  const onDelete = () => {
    handleClose()
    setDialogType('delete')
    setOpenDialog(true)
  }

  const onChangeVersion = () => {
    setDialogType('changeVersion')
    handleClose()
    setOpenDialog(true)
  }

  const handleChangeVersion = (event: SelectChangeEvent) => {
    console.log(event.target.value)
    setPracticeVersion(event.target.value)
  }

  const handleDelete = () => {
    deleteExam.mutate(
      {
        endpoint: `/exams/${examId}`
      },
      {
        onSuccess: () => {
          showToast('Practice Test deleted successfully', 'success')
        },

        onError: (res: any) => {
          showToast(res.response.data.message, 'error')
        }
      }
    )
    setOpenDialog(false)
  }

  const handleAddNew = (data: { examName: string }) => {
    createExam.mutate(
      {
        endpoint: `/courses/${courseId}/exams`,
        data
      },
      {
        onSuccess: () => {
          showToast('Exam created successfully', 'success')
        },

        onError: (res: any) => {
          showToast(res.response.data.message, 'error')
        }
      }
    )
    setOpenDialog(false)
    reset()
  }

  const handleChangeExamVersion = () => {
    if (!practiceVersion) {
      setOpenDialog(false)
      reset()
      showToast('Failed to change exam version', 'error')

      return
    }

    updateExam.mutate(
      {
        endpoint: `/exams/${examId}`,
        data: { currentVersionId: Number(practiceVersion) }
      },
      {
        onSuccess: () => {
          showToast('Exam version changed successfully', 'success')
        },

        onError: (res: any) => {
          showToast(res.response.data.message, 'error')
        }
      }
    )
    setOpenDialog(false)
    reset()
  }

  const dialogContentList = (): { [key: string]: DialogProps } => ({
    delete: {
      title: 'Delete Practice Test?',
      content: (
        <Typography>Are you sure you want to delete this practice? This is permanent and cannot be undone.</Typography>
      ),
      actions: 'Delete',
      handleDialogAction: () => handleDelete()
    },
    changeVersion: {
      title: 'Change Version',
      content: (
        <FormControl fullWidth size='small' className='w-[560px]'>
          <InputLabel>Practice Test Version</InputLabel>
          <Select value={practiceVersion} label='Practice Test Version' onChange={handleChangeVersion}>
            {isLoading ? (
              <CircularProgress />
            ) : (
              examVersions?.map(examVersion => (
                <MenuItem value={examVersion.examVersionId} key={examVersion.examVersionId}>
                  Exam Version {examVersion.versionNumber}
                </MenuItem>
              ))
            )}
          </Select>
        </FormControl>
      ),
      actions: 'Change',

      // handleDialogAction: () => setOpenDialog(false)
      handleDialogAction: handleChangeExamVersion
    },
    addNew: {
      title: 'Create new Practices test',
      content: (
        <>
          <Controller
            name='examName'
            control={control}
            rules={{
              required: 'This field is required',
              validate: value => (value.trim() ? true : 'This field is required')
            }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                type='text'
                label='Exam Name'
                placeholder='Practice Exams | AWS Certified Solutions Architect Associate'
                size='small'
                className='min-w-[560px]'
              />
            )}
          />
          {errors?.examName && (
            <Typography variant='body2' className='text-red-500'>
              {errors?.examName.message}
            </Typography>
          )}
        </>
      ),
      actions: 'Create',
      handleDialogAction: handleSubmit(handleAddNew)
    }
  })

  const handleEdit = () => {
    createPracticePlan.mutate(
      {
        endpoint: `/exams/${examId}/versions`,
        data: { examId: Number(examId) }
      },
      {
        onSuccess: (data: any) => {
          router.push(`/practice-test/${examId}/${data.data.examVersionId}`)
        },
        onError: (res: any) => {
          showToast(res.response.data.message, 'error')
        }
      }
    )
  }

  const renderActions = (row: Exam) => {
    return (
      <div className='flex gap-2'>
        <IconButton
          className='bg-transparent'
          id='demo-positioned-button'
          aria-controls={open ? 'demo-positioned-menu' : undefined}
          aria-haspopup='true'
          aria-expanded={open ? 'true' : undefined}
          onClick={event => {
            setAnchorEl(event.currentTarget)
            setExamId(row.examId.toString())
            setPracticeVersion(row.currentVersionId ? row.currentVersionId.toString() : '')
          }}
        >
          <i className='ri-more-2-fill'></i>
        </IconButton>
        <Menu
          id='demo-positioned-menu'
          aria-labelledby='demo-positioned-button'
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'left'
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left'
          }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={onChangeVersion}>Change Version</MenuItem>
          <MenuItem onClick={onDelete} className='text-rose-500'>
            Delete
          </MenuItem>
        </Menu>
      </div>
    )
  }

  return (
    <Card className='px-4 py-6'>
      <Typography variant='h5' className='mb-2'>
        Practice Test
      </Typography>
      <Divider />
      <div className='flex justify-between mt-4'>
        <div className='mb-4 flex gap-3 items-center h-10'>
          <Button variant='contained' className='whitespace-nowrap' onClick={onAddNew}>
            Add New
          </Button>
          <TextField
            fullWidth
            type='text'
            placeholder='Filter exam...'
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            size='small'
          />
        </div>
        <Dropdown columns={columns} selectedColumns={selectedColumns} onToggleColumn={handleColumnToggle} />
      </div>
      <TableCustom
        columns={columns}
        data={data?.exams || []}
        selectedColumns={selectedColumns}
        page={page}
        renderCell={renderCell}
        rowsPerPage={rowsPerPage}
        handleChangePage={handleChangePage}
        handleChangeRowsPerPage={handleChangeRowsPerPage}
        actions={renderActions}
        onActionClick={() => {}}
        actionType='dynamic'
        loading={loading}
        total={data?.meta.totalCount || 0}
        messageDataFound='No exams found'
      />
      <DialogCustom
        openDialog={openDialog}
        setOpenDialog={setOpenDialog}
        dialogTitle={dialogContentList()[dialogType].title}
        dialogContent={dialogContentList()[dialogType].content}
        dialogActions={dialogContentList()[dialogType].actions}
        handleDialogAction={dialogContentList()[dialogType].handleDialogAction}
        handleClose={handleClose}
      />
    </Card>
  )
}

export default PracticeTest
