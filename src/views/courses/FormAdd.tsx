// React Imports
import { useEffect, useState } from 'react'

import dynamic from 'next/dynamic'

// MUI Imports
import Card from '@mui/material/Card'
import Grid from '@mui/material/Grid'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  TextField,
  CircularProgress,
  FormHelperText
} from '@mui/material'

// Utils
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useForm, Controller } from 'react-hook-form'

import { convertS3ToCloudFrontUrl, convertToSlug, formatNumber, unformedNumber } from '@/utils'
import type { Course } from '@/api/courses'
import { useApiPut } from '@/hooks/useApi'
import type { CourseFormData } from '@/types/pages/course'
import { useToast } from '@/contexts/ToastContext'
import ImageUpload from '../upload/ImageUpload'

const Editor = dynamic(() => import('@/components/Editor'), {
  ssr: false
})

const instructorsList = ['Phong Nguyen', 'Minh Bui', 'Tin Tran']

interface Props {
  title: string
  onSubmit?: (data: Course) => void
  buttonText: string
  id: string | undefined
  course: Course | undefined
  isLoading: boolean
}

const initialData: Course = {
  courseName: '',
  shortName: '',
  headline: '',
  slug: '',
  examLanguage: '',
  originalPrice: undefined,
  salePrice: undefined,
  requirement: '',
  description: '',
  target: '',
  coverImage: '',
  accessDuration: undefined,
  isPublished: false,
  explainLanguage: '',
  courseId: 0
}

const courseSchema = yup.object({
  courseName: yup
    .string()
    .trim()
    .required('Course name is required')
    .max(255, 'Course name must be less than 255 characters'),
  shortName: yup
    .string()
    .trim()
    .required('Short name is required')
    .max(50, 'Short name must be less than 50 characters'),
  slug: yup.string().trim().required('Slug is required'),
  headline: yup.string().optional().nullable(),
  examLanguage: yup.string().nullable().optional(),
  originalPrice: yup.string().nullable().optional(),
  salePrice: yup.string().nullable().optional(),
  requirement: yup.string().nullable().optional(),
  description: yup.string().nullable().optional(),
  target: yup.string().nullable().optional(),
  coverImage: yup.string().nullable().optional(),
  accessDuration: yup.number().nullable().optional(),
  explainLanguage: yup.string().nullable().optional()

  // instructors: yup.array().of(yup.string()).optional().nullable()
})

const resolver = yupResolver(courseSchema)

const FormAdd: React.FC<Props> = ({ title, id, course, isLoading }) => {
  const [instructors, setInstructors] = useState<string[]>([])
  const { showToast } = useToast()

  const updateCourse = useApiPut<Course, Partial<Course>>()

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<CourseFormData>({
    resolver: resolver,
    defaultValues: {
      ...initialData
    }
  })

  const courseName = watch('courseName')

  const onFormSubmit = (data: CourseFormData) => {
    updateCourse.mutate(
      {
        endpoint: `/courses/${id}`,
        data
      },
      {
        onSuccess: () => {
          showToast('Course updated successfully', 'success')
        },
        onError: (res: any) => {
          showToast(res.response.data.message, 'error')
        }
      }
    )
  }

  // Auto-generate slug when course name changes
  useEffect(() => {
    if (courseName) {
      const newSlug = convertToSlug(courseName, '')

      setValue('slug', newSlug, { shouldValidate: true })
    }
  }, [courseName, setValue])

  // Set form values when course data is loaded
  useEffect(() => {
    if (course) {
      reset({
        courseName: course.courseName ?? '',
        shortName: course.shortName ?? '',
        headline: course.headline ?? '',
        slug: course.slug ?? '',
        examLanguage: course.examLanguage ?? 'English',
        originalPrice: course.originalPrice ?? undefined,
        salePrice: course.salePrice ?? undefined,
        requirement: course.requirement ?? '',
        description: course.description ?? '',
        target: course.target ?? '',
        coverImage: course.coverImage ?? '',
        accessDuration: course.accessDuration ?? undefined,
        isPublished: course.isPublished ?? false,
        explainLanguage: course.explainLanguage ?? 'English'
      })
    }
  }, [course, reset])

  return (
    <Card className='max-w-[68%] m-auto'>
      <CardHeader title={title} />

      <CardContent>
        {isLoading ? (
          <div className='flex justify-center'>
            <CircularProgress color='primary' />
          </div>
        ) : (
          <form onSubmit={handleSubmit(onFormSubmit)}>
            <Grid container spacing={5}>
              {/* Course Name */}
              <Grid item xs={12}>
                <Controller
                  name='courseName'
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Course Name'
                      placeholder='Practice Exams | AWS Certified Solutions Architect Associate'
                      size='small'
                      error={!!errors.courseName}
                      helperText={errors.courseName?.message}
                    />
                  )}
                />
              </Grid>

              {/* Slug */}
              <Grid item xs={12}>
                <Controller
                  name='slug'
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Slug'
                      placeholder='Slug'
                      size='small'
                      error={!!errors.slug}
                      helperText={errors.slug?.message}
                    />
                  )}
                />
              </Grid>

              {/* Short Name */}
              <Grid item xs={12}>
                <Controller
                  name='shortName'
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Short Name'
                      placeholder='AWS-SAA-C03'
                      size='small'
                      error={!!errors.shortName}
                      helperText={errors.shortName?.message}
                    />
                  )}
                />
              </Grid>

              {/* Headline */}
              <Grid item xs={12}>
                <Controller
                  name='headline'
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Headline'
                      placeholder='Prepare for your SAA-C03 exam. 390 high-quality practice test questions written from scratch with detailed explanations!'
                      size='small'
                      error={!!errors.headline}
                      helperText={errors.headline?.message}
                    />
                  )}
                />
              </Grid>

              {/* Exam Language */}
              <Grid item xs={12}>
                <Controller
                  name='examLanguage'
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth size='small' error={!!errors.examLanguage}>
                      <InputLabel>Exam Language</InputLabel>
                      <Select {...field} label='Exam Language'>
                        <MenuItem value='English'>English</MenuItem>
                        <MenuItem value='Japanese'>Japanese</MenuItem>
                        <MenuItem value='Vietnamese'>Vietnamese</MenuItem>
                      </Select>
                      {errors.examLanguage && <FormHelperText>{errors.examLanguage.message}</FormHelperText>}
                    </FormControl>
                  )}
                />
              </Grid>

              {/* Explanation Language */}
              <Grid item xs={12}>
                <Controller
                  name='explainLanguage'
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth size='small' error={!!errors.explainLanguage}>
                      <InputLabel>Explanation Language</InputLabel>
                      <Select {...field} label='Explanation Language'>
                        <MenuItem value='English'>English</MenuItem>
                        <MenuItem value='Japanese'>Japanese</MenuItem>
                        <MenuItem value='Vietnamese'>Vietnamese</MenuItem>
                      </Select>
                      {errors.explainLanguage && <FormHelperText>{errors.explainLanguage.message}</FormHelperText>}
                    </FormControl>
                  )}
                />
              </Grid>

              {/* Instructors */}
              <Grid item xs={12}>
                <FormControl fullWidth size='small'>
                  <InputLabel>Instructors</InputLabel>
                  <Select
                    multiple
                    label='Instructors'
                    value={instructors}
                    onChange={event => setInstructors(event.target.value as string[])}
                    renderValue={selected => (
                      <div className='flex flex-wrap gap-2'>
                        {(selected as string[]).map(value => (
                          <Chip
                            key={value}
                            clickable
                            deleteIcon={
                              <i className='ri-close-circle-fill' onMouseDown={event => event.stopPropagation()} />
                            }
                            size='small'
                            label={value}
                          />
                        ))}
                      </div>
                    )}
                  >
                    {instructorsList.map(name => (
                      <MenuItem key={name} value={name}>
                        {name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Original Price */}
              <Grid item xs={12}>
                <Controller
                  name='originalPrice'
                  control={control}
                  render={({ field }) => (
                    <TextField
                      fullWidth
                      label='Original Price (VND)'
                      placeholder='Original Price (VND)'
                      size='small'
                      value={field.value !== undefined && field.value !== null ? formatNumber(+field?.value, ',') : ''}
                      onChange={e => {
                        const rawValue = unformedNumber(e.target.value)

                        if (!isNaN(Number(rawValue))) {
                          field.onChange(Number(rawValue))
                        }
                      }}
                      error={!!errors.originalPrice}
                      helperText={errors.originalPrice?.message}
                    />
                  )}
                />
              </Grid>

              {/* Sale Price */}
              <Grid item xs={12}>
                <Controller
                  name='salePrice'
                  control={control}
                  render={({ field }) => (
                    <TextField
                      fullWidth
                      label='Sale Price (VND)'
                      placeholder='Sale Price (VND)'
                      size='small'
                      value={field.value !== undefined && field.value !== null ? formatNumber(+field.value, ',') : ''}
                      onChange={e => {
                        const rawValue = unformedNumber(e.target.value)

                        if (!isNaN(Number(rawValue))) {
                          field.onChange(Number(rawValue))
                        }
                      }}
                      error={!!errors.salePrice}
                      helperText={errors.salePrice?.message}
                    />
                  )}
                />
              </Grid>

              {/* Access Duration */}
              <Grid item xs={12}>
                <Controller
                  name='accessDuration'
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Access Duration (Month)'
                      type='number'
                      size='small'
                      error={!!errors.accessDuration}
                      helperText={errors.accessDuration?.message}
                    />
                  )}
                />
              </Grid>

              {/* Image Upload */}
              <Grid item xs={12}>
                <CardContent className='pb-0'>
                  <div className='flex max-sm:flex-col items-center gap-6'>
                    <Controller
                      name='coverImage'
                      control={control}
                      render={({ field }) => (
                        <>
                          <ImageUpload
                            onReset={() => {
                              field.onChange(
                                course?.coverImage ? convertS3ToCloudFrontUrl(course?.coverImage) : '/images/empty.webp'
                              )
                            }}
                            onFile='courses'
                            defaultImage={field.value ? convertS3ToCloudFrontUrl(field.value) : '/images/empty.webp'}
                            onImageUploaded={imageUrl => field.onChange(imageUrl)}
                            maxSize={800}
                            allowedFormats='image/png, image/jpeg, image/gif'
                          />
                        </>
                      )}
                    />
                  </div>
                </CardContent>
              </Grid>

              {/* Requirements */}
              <Grid item xs={12} className='pt-4'>
                <Typography variant='h6' className='mbe-2'>
                  Requirements
                </Typography>
                <Controller
                  name='requirement'
                  control={control}
                  render={({ field }) => (
                    <Editor value={field.value ?? ''} onChange={content => field.onChange(content)} />
                  )}
                />
              </Grid>

              {/* Descriptions */}
              <Grid item xs={12} className='pt-4'>
                <Typography variant='h6' className='mbe-2'>
                  Descriptions
                </Typography>
                <Controller
                  name='description'
                  control={control}
                  render={({ field }) => (
                    <Editor value={field.value ?? ''} onChange={content => field.onChange(content)} />
                  )}
                />
              </Grid>

              {/* Target Audience */}
              <Grid item xs={12} className='pt-4'>
                <Typography variant='h6' className='mbe-2'>
                  Who this course is for
                </Typography>
                <Controller
                  name='target'
                  control={control}
                  render={({ field }) => (
                    <Editor value={field.value ?? ''} onChange={content => field.onChange(content)} />
                  )}
                />
              </Grid>

              {/* Submit Button */}
              <Grid item xs={12} className='pt-7'>
                <div className='flex items-center justify-between flex-wrap gap-5'>
                  <Button
                    variant='contained'
                    type='submit'
                    disabled={updateCourse.isLoading}
                    loading={updateCourse.isLoading}
                  >
                    Save
                  </Button>
                </div>
              </Grid>
            </Grid>
          </form>
        )}
      </CardContent>
    </Card>
  )
}

export default FormAdd
