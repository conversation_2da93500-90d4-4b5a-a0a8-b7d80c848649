import React from 'react'

// MUI Imports
import { Box, CircularProgress, Dialog, DialogContent, DialogTitle, IconButton, Link, Typography } from '@mui/material'

import type { Instructor } from '@/api/instructor'

interface Props {
  openDialog: boolean
  setOpenDialog: (value: boolean) => void
  info: Instructor | null
  isLoading: boolean
}

const DialogCustom: React.FC<Props> = ({ openDialog, setOpenDialog, isLoading, info }) => {
  return (
    <Dialog
      open={openDialog}
      onClose={() => setOpenDialog(false)}
      aria-labelledby='customized-dialog-title'
      PaperProps={{
        sx: {
          maxWidth: '60%'
        }
      }}
    >
      {isLoading ? (
        <Box className='w-[500px] h-[500px] relative'>
          <IconButton
            aria-label='close'
            onClick={() => setOpenDialog(false)}
            sx={theme => ({
              position: 'absolute',
              right: 8,
              top: 8,
              color: theme.palette.grey[500]
            })}
          >
            <i className='ri-close-line'></i>
          </IconButton>
          <Box className='absolute top-[50%] left-[50%]'>
            <CircularProgress />
          </Box>
        </Box>
      ) : (
        <>
          <DialogTitle id='customized-dialog-title'>
            <Box className='flex items-center gap-5'>
              <img height={100} width={100} className='rounded' src={'/images/avatars/1.png'} alt='Profile' />
              <Box className='flex flex-col gap-2'>
                <Typography variant='h5'>{info?.firstName + ' ' + info?.lastName}</Typography>
                <Typography variant='h5' color='textSecondary'>
                  {info?.headline}
                </Typography>
                <Link href={info?.facebookUrl}>Facebook Url</Link>
              </Box>
            </Box>
          </DialogTitle>
          <IconButton
            aria-label='close'
            onClick={() => setOpenDialog(false)}
            sx={theme => ({
              position: 'absolute',
              right: 8,
              top: 8,
              color: theme.palette.grey[500]
            })}
          >
            <i className='ri-close-line'></i>
          </IconButton>
          <DialogContent className='pt-1'>
            <Typography variant='h5'>Biography</Typography>
            <Typography
              variant='h5'
              color={'textSecondary'}
              className='mt-3 min-w-[500px] min-h-[250px]'
              dangerouslySetInnerHTML={{
                __html: info?.bio || ''
              }}
            />
          </DialogContent>
        </>
      )}
    </Dialog>
  )
}

export default DialogCustom
