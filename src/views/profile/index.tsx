import { useState } from 'react'

import dynamic from 'next/dynamic'

import { Box, Button, Card, CardContent, TextField, Typography } from '@mui/material'
import { Controller, useForm } from 'react-hook-form'

import ImageUpload from '../upload/ImageUpload'

const Editor = dynamic(() => import('@/components/Editor'), {
  ssr: false
})

const Profile = () => {
  const [profileImage, setProfileImage] = useState('/images/avatars/1.png')

  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm()

  const handleImageUploaded = (imageUrl: string) => {
    setProfileImage(imageUrl)
  }

  const onSubmit = handleSubmit(data => {
    const formData = {
      ...data,
      profileImage
    }

    console.log(formData)
  })

  return (
    <Card>
      <CardContent className='max-w-[68%]'>
        <form noValidate autoComplete='off' onSubmit={onSubmit}>
          <Box className='flex flex-col gap-5'>
            <ImageUpload
              onFile='instructors'
              defaultImage={profileImage}
              onImageUploaded={handleImageUploaded}
              maxSize={800}
              allowedFormats='image/png, image/jpeg, image/gif'
            />

            <Box className='flex gap-5'>
              <Box className='w-full'>
                <Controller
                  name='firstName'
                  control={control}
                  rules={{
                    required: 'This field is required',
                    validate: value => (value.trim() ? true : 'This field is required')
                  }}
                  render={({ field }) => <TextField {...field} size='small' fullWidth required label='First name' />}
                />
                {errors?.firstName && (
                  <Typography variant='body2' color='error' className='mt-1'>
                    {errors?.firstName?.message?.toString()}
                  </Typography>
                )}
              </Box>
              <Box className='w-full'>
                <Controller
                  name='lastName'
                  control={control}
                  rules={{
                    required: 'This field is required',
                    validate: value => (value.trim() ? true : 'This field is required')
                  }}
                  render={({ field }) => <TextField {...field} size='small' fullWidth required label='Last Name' />}
                />
                {errors?.lastName && (
                  <Typography variant='body2' color='error' className='mt-1'>
                    {errors?.lastName?.message?.toString()}
                  </Typography>
                )}
              </Box>
            </Box>

            {/* Phần còn lại của form... */}
            <Box className='flex gap-5'>
              <Box className='w-full'>
                <Controller
                  name='headline'
                  control={control}
                  rules={{
                    required: 'This field is required',
                    validate: value => (value.trim() ? true : 'This field is required'),
                    maxLength: {
                      value: 41,
                      message: 'Maximum length is 41 characters'
                    }
                  }}
                  render={({ field }) => {
                    return (
                      <TextField
                        {...field}
                        size='small'
                        fullWidth
                        label='Headline'
                        InputProps={{
                          endAdornment: <span style={{ color: 'gray' }}>41</span>
                        }}
                      />
                    )
                  }}
                />
                {errors?.headline && (
                  <Typography variant='body2' color='error' className='mt-1'>
                    {errors?.headline?.message?.toString()}
                  </Typography>
                )}
              </Box>
              <Box className='w-full'>
                <Controller
                  name='facebookLink'
                  control={control}
                  rules={{
                    required: 'This field is required',
                    validate: value => (value.trim() ? true : 'This field is required')
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size='small'
                      fullWidth
                      className='rounded-s-0'
                      InputProps={{
                        startAdornment: <span style={{ color: 'gray' }}>http://www.facebook.com/</span>
                      }}
                    />
                  )}
                />
                {errors?.facebookLink && (
                  <Typography variant='body2' color='error' className='mt-1'>
                    {errors?.facebookLink?.message?.toString()}
                  </Typography>
                )}
              </Box>
            </Box>

            <Box className='w-full'>
              <Typography variant='h6' className='mb-2'>
                Biography
              </Typography>
              <Box className='w-full'>
                <Controller
                  name='biography'
                  control={control}
                  rules={{
                    required: 'This field is required',
                    validate: value => value.replace(/<[^>]*>/g, '').trim() !== '' || 'This field is required'
                  }}
                  render={({ field: { ref, ...field } }) => <Editor {...field} onEditorRef={ref} />}
                />
                {errors?.biography && (
                  <Typography variant='body2' color='error' className='mt-1'>
                    {errors?.biography?.message?.toString()}
                  </Typography>
                )}
              </Box>
            </Box>

            <Box>
              <Button variant='contained' size='small' type='submit'>
                Save
              </Button>
            </Box>
          </Box>
        </form>
      </CardContent>
    </Card>
  )
}

export default Profile
