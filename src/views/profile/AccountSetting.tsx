import { <PERSON>, <PERSON><PERSON>, Card, CardContent, TextField, Typography } from '@mui/material'
import { Controller, useForm } from 'react-hook-form'

import PasswordInput from '@/components/PasswordInput'

const AccountSetting = () => {
  const {
    control,
    handleSubmit,
    watch,
    formState: { errors }
  } = useForm()

  const onSubmit = handleSubmit(data => {
    console.log(data)
  })

  const newPassword = watch('newPassword')

  return (
    <Card>
      <CardContent className='max-w-[68%]'>
        <form noValidate autoComplete='off' onSubmit={onSubmit}>
          <Box className='flex flex-col gap-5'>
            <Controller
              name='email'
              control={control}
              rules={{
                required: 'This field is required',
                validate: value => (value.trim() ? true : 'This field is required')
              }}
              render={({ field }) => (
                <TextField
                  disabled
                  {...field}
                  size='small'
                  fullWidth
                  value={'<EMAIL>'}
                  className='rounded-s-0'
                />
              )}
            />
            <Box className='w-full'>
              <Controller
                name='currentPassword'
                control={control}
                rules={{
                  required: 'Current Password is required',
                  validate: value => (value.trim() ? true : 'This field is required'),
                  minLength: {
                    value: 8,
                    message: 'Password must be at least 8 characters long'
                  }
                }}
                render={({ field }) => (
                  <PasswordInput label='Current Password' value={field.value || ''} onChange={field.onChange} />
                )}
              />
              {errors?.currentPassword && (
                <Typography variant='body2' color='error' className='mt-1'>
                  {errors?.currentPassword?.message?.toString()}
                </Typography>
              )}
            </Box>
            <Box className='w-full'>
              <Controller
                name='newPassword'
                control={control}
                rules={{
                  required: 'New Password is required',
                  validate: value => (value.trim() ? true : 'This field is required'),
                  minLength: {
                    value: 8,
                    message: 'Password must be at least 8 characters long'
                  }
                }}
                render={({ field }) => (
                  <PasswordInput label='New Password' value={field.value || ''} onChange={field.onChange} />
                )}
              />
              {errors?.newPassword && (
                <Typography variant='body2' color='error' className='mt-1'>
                  {errors?.newPassword?.message?.toString()}
                </Typography>
              )}
            </Box>

            <Box className='w-full'>
              <Controller
                name='confirmPassword'
                control={control}
                rules={{
                  required: 'Confirm Password is required',
                  validate: value => value === newPassword || 'Passwords do not match'
                }}
                render={({ field }) => (
                  <PasswordInput label='Confirm Password' value={field.value || ''} onChange={field.onChange} />
                )}
              />
              {errors?.confirmPassword && (
                <Typography variant='body2' color='error' className='mt-1'>
                  {errors?.confirmPassword?.message?.toString()}
                </Typography>
              )}
            </Box>
            <Box>
              <Button variant='contained' size='small' type='submit'>
                Change Password
              </Button>
            </Box>
          </Box>
        </form>
      </CardContent>
    </Card>
  )
}

export default AccountSetting
