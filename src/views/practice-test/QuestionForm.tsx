import { useRef } from 'react'

import dynamic from 'next/dynamic'

import {
  Box,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  Checkbox,
  CircularProgress,
  Grid,
  IconButton,
  Radio,
  Typography
} from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'

import type { Question } from '@/api/question'
import { useApiPost } from '@/hooks/useApi'

const Editor = dynamic(() => import('@/components/Editor'), {
  ssr: false,
  loading: () => <CircularProgress color='primary' />
})

interface Props {
  handleDeleteQuestion: (id: number) => void
  index: number
  formData: Question
  setTitle: (title: string) => void
  onCompleteTabChange: (question: Question) => void
  type: string | null
  handleFormSubmit: (data: Question) => void
}

const QuestionForm: React.FC<Props> = ({ formData, index, setTitle, handleDeleteQuestion, handleFormSubmit, type }) => {
  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    formState: { errors, isDirty }
  } = useFormContext<Question>()

  const formRef = useRef<HTMLFormElement>(null)
  const createQuestion = useApiPost<Question, Partial<Question>>()

  return (
    <Card className='h-full max-w-[88%] m-auto relative'>
      <CardHeader
        title={`Question ${index + 1} - ${type === 'multipleChoice' ? 'Multiple Choice' : 'Multiple Selection'}`}
        className='ml-11 capitalize'
      />
      <CardContent>
        <form ref={formRef} onSubmit={handleSubmit(handleFormSubmit)}>
          {/* Question Text */}
          <Grid item xs={12} className='py-4 ml-11'>
            <Typography variant='body1' className='mbe-2 font-bold' color='text.primary'>
              Question
            </Typography>
            <Box className='flex justify-between'>
              <Controller
                name='questionText'
                control={control}
                rules={{
                  required: 'This field is required',
                  validate: value => value.replace(/<[^>]*>/g, '').trim() !== '' || 'This field is required'
                }}
                render={({ field: { ref, ...field } }) => (
                  <Editor
                    className='w-[95%]'
                    {...field}
                    onEditorRef={ref}
                    optionalToolbar
                    onChange={value => {
                      field.onChange(value)
                      setTitle(value)
                    }}
                  />
                )}
              />
              <IconButton className='opacity-0 w-11'>
                <i className='ri-delete-bin-6-fill'></i>
              </IconButton>
            </Box>
            {errors.questionText && (
              <Typography className='mt-1 text-error' variant='caption'>
                {errors.questionText.message}
              </Typography>
            )}
          </Grid>
          {/* Answers */}
          <Controller
            name='answers'
            control={control}
            defaultValue={[]}
            render={({ field }) => (
              <>
                {(field.value || []).map((answer, index) => (
                  <Grid key={index} item xs={12} className='pt-4'>
                    <Typography variant='body1' className='mbe-2 font-bold ml-11' color='text.primary'>
                      {`Answer ${index + 1}`}
                    </Typography>
                    <Box className='flex items-start gap-2 w-full'>
                      {type === 'multipleChoice' ? (
                        <Radio
                          checked={
                            Array.isArray(getValues('correctAnswer')) && getValues('correctAnswer').includes(answer)
                          }
                          onChange={() => {
                            // For radio buttons, set the single correct answer
                            setValue('correctAnswer', [answer], {
                              shouldDirty: true,
                              shouldTouch: true,
                              shouldValidate: true
                            })
                          }}
                        />
                      ) : (
                        <Checkbox
                          checked={
                            Array.isArray(getValues('correctAnswer')) && getValues('correctAnswer').includes(answer)
                          }
                          onChange={e => {
                            // Ensure we have an array to work with
                            const currentCorrectAnswers = Array.isArray(getValues('correctAnswer'))
                              ? [...getValues('correctAnswer')]
                              : []

                            let newCorrectAnswers

                            if (e.target.checked) {
                              // Add to correct answers if checked
                              newCorrectAnswers = [...currentCorrectAnswers, answer]
                            } else {
                              // Remove from correct answers if unchecked
                              newCorrectAnswers = currentCorrectAnswers.filter(a => a !== answer)
                            }

                            // Update with full options to ensure form state updates
                            setValue('correctAnswer', newCorrectAnswers, {
                              shouldDirty: true,
                              shouldTouch: true,
                              shouldValidate: true
                            })
                          }}
                        />
                      )}
                      <Box className='flex items-center justify-between w-full'>
                        <Controller
                          name={`answers.${index}` as const}
                          control={control}
                          rules={{
                            required: 'This field is required',
                            validate: value => value.replace(/<[^>]*>/g, '').trim() !== '' || 'This field is required'
                          }}
                          render={({ field: { ref, ...field } }) => (
                            <Editor
                              {...field}
                              value={field.value?.toString() || ''}
                              onEditorRef={ref}
                              onChange={value => {
                                field.onChange(value)

                                // Force form state update
                                isDirty // Access to trigger recalculation
                              }}
                              optionalToolbar
                              className='w-[95%]'
                            />
                          )}
                        />
                        {index > 1 && (
                          <IconButton
                            onClick={() => {
                              const currentAnswers = [...(getValues('answers') || [])]
                              const answerToRemove = currentAnswers[index]

                              // Remove the answer
                              currentAnswers.splice(index, 1)
                              setValue('answers', currentAnswers, {
                                shouldDirty: true,
                                shouldTouch: true,
                                shouldValidate: true
                              })

                              // Update correctAnswer if the removed answer was selected
                              const currentCorrectAnswers = Array.isArray(getValues('correctAnswer'))
                                ? [...getValues('correctAnswer')]
                                : []

                              if (currentCorrectAnswers.includes(answerToRemove)) {
                                setValue(
                                  'correctAnswer',
                                  currentCorrectAnswers.filter(a => a !== answerToRemove),
                                  {
                                    shouldDirty: true,
                                    shouldTouch: true,
                                    shouldValidate: true
                                  }
                                )
                              }
                            }}
                          >
                            <i className='ri-delete-bin-6-fill'></i>
                          </IconButton>
                        )}
                      </Box>
                    </Box>
                    {errors?.answers?.[index] && (
                      <Typography className='ml-11 mt-1 text-error' variant='caption'>
                        {errors?.answers?.[index]?.message}
                      </Typography>
                    )}
                  </Grid>
                ))}
                <Grid item xs={12} className='mt-2'>
                  <Button
                    variant='outlined'
                    startIcon={<i className='ri-add-line'></i>}
                    onClick={() => {
                      const currentAnswers = Array.isArray(getValues('answers')) ? [...getValues('answers')] : []

                      setValue('answers', [...currentAnswers, ''], {
                        shouldDirty: true,
                        shouldTouch: true,
                        shouldValidate: true
                      })

                      // Initialize correctAnswer if it doesn't exist
                      if (!getValues('correctAnswer')) {
                        setValue('correctAnswer', [], {
                          shouldDirty: true
                        })
                      }
                    }}
                    className='ml-11'
                  >
                    Add Answer
                  </Button>
                </Grid>
              </>
            )}
          />
          {errors.correctAnswer && (
            <Typography className='ml-11 mt-1 text-error' variant='caption'>
              {errors.correctAnswer.message}
            </Typography>
          )}
          {/* Explanation */}
          <Grid item xs={12} className='pt-4 ml-11'>
            <Typography variant='body1' className='mbe-2 font-bold' color='text.primary'>
              Overall explanation
            </Typography>
            <Box className='flex justify-between'>
              <Controller
                name='explanation'
                control={control}
                rules={{
                  required: 'This field is required',
                  validate: value => value !== '<p><br></p>' || 'This field is required'
                }}
                render={({ field: { ref, ...field } }) => (
                  <Editor {...field} onEditorRef={ref} className='w-[95%]' optionalToolbar />
                )}
              />
              <IconButton className='opacity-0 w-11'>
                <i className='ri-delete-bin-6-fill'></i>
              </IconButton>
            </Box>
            {errors.explanation && (
              <Typography className='mt-1 text-error' variant='caption'>
                {errors.explanation.message}
              </Typography>
            )}
          </Grid>
          {/* Submit Button */}
          <Grid item xs={12} className='pt-4 ml-11'>
            <Button variant='contained' type='submit' loading={createQuestion.isLoading}>
              Save Question
            </Button>
          </Grid>
        </form>
      </CardContent>
      {/* Delete Question Button */}
      <Button
        className='absolute top-4 right-12'
        startIcon={<i className='ri-delete-bin-6-fill' style={{ fontSize: 20 }}></i>}
        onClick={() => handleDeleteQuestion(formData.questionId)}
        color='error'
      >
        Delete
      </Button>
    </Card>
  )
}

QuestionForm.displayName = 'QuestionForm'
export default QuestionForm
