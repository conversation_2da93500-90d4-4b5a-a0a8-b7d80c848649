import React, { useEffect, useRef } from 'react'

import dynamic from 'next/dynamic'

import { Controller, useFormContext } from 'react-hook-form'
import {
  Card,
  CardHeader,
  CardContent,
  Grid,
  Typography,
  Box,
  Button,
  IconButton,
  Radio,
  Checkbox,
  CircularProgress
} from '@mui/material'

import { getRandomId } from '@/utils'
import type { NewQuestion } from '@/api/question'

const Editor = dynamic(() => import('@/components/Editor'), {
  ssr: false,
  loading: () => <CircularProgress color='primary' />
})

interface Props {
  handleDeleteQuestion: (id: number) => void
  index: number
  type: string | null
  handleFormSubmit: (data: NewQuestion) => void
  setTitle: (title: string) => void
  loading: boolean
}

const QuestionEmptyForm: React.FC<Props> = ({
  loading,
  index,
  handleDeleteQuestion,
  type,
  setTitle,
  handleFormSubmit
}) => {
  const {
    control,
    getValues,
    setValue,
    handleSubmit,
    clearErrors,
    formState: { errors }
  } = useFormContext<NewQuestion>()

  const formRef = useRef<HTMLFormElement>(null)

  useEffect(() => {
    setValue('answers', [...getValues('answers').filter(answer => answer.content !== undefined)])
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <Card className='h-full max-w-[88%] m-auto relative'>
      <CardHeader
        title={`Question ${index + 1} - ${type === 'multipleChoice' ? 'Multiple Choice' : 'Multiple Selection'}`}
        className='ml-11 capitalize'
      />
      <CardContent>
        <form ref={formRef} onSubmit={handleSubmit(handleFormSubmit)}>
          {/* Question Text */}
          <Grid item xs={12} className='py-4 ml-11'>
            <Typography variant='body1' className='mbe-2 font-bold' color='text.primary'>
              Question
            </Typography>
            <Box className='flex justify-between'>
              <Controller
                name='questionText'
                control={control}
                rules={{
                  required: 'This field is required',
                  validate: value => {
                    const stripHtml = typeof value === 'string' ? value.replace(/<[^>]*>/g, '').trim() : ''

                    return stripHtml !== '' || 'This field is required'
                  }
                }}
                render={({ field: { ref, ...field } }) => (
                  <Editor
                    className='w-[95%]'
                    {...field}
                    onEditorRef={ref}
                    optionalToolbar
                    onChange={value => {
                      field.onChange(value)
                      setTitle(value)
                    }}
                  />
                )}
              />
              <IconButton className='opacity-0 w-11'>
                <i className='ri-delete-bin-6-fill'></i>
              </IconButton>
            </Box>
            {errors.questionText && (
              <Typography className='mt-1 text-error' variant='caption'>
                {errors.questionText.message}
              </Typography>
            )}
          </Grid>

          {/* Answers */}
          <Controller
            name='answers'
            control={control}
            render={({ field }) => (
              <>
                {(field.value || []).map((answer, index) => (
                  <Grid key={answer.id || index} item xs={12} className='pt-4'>
                    <Typography variant='body1' className='mbe-2 font-bold ml-11' color='text.primary'>
                      {`Answer ${index + 1}`}
                    </Typography>
                    <Box className='flex items-start gap-2 w-full'>
                      {type === 'multipleChoice' ? (
                        <Radio
                          checked={getValues('correctAnswer') ? getValues('correctAnswer').includes(answer.id) : false}
                          onChange={() => {
                            setValue('correctAnswer', [answer.id], {
                              shouldDirty: true,
                              shouldTouch: true,
                              shouldValidate: true
                            })
                            clearErrors('correctAnswer')
                          }}
                        />
                      ) : (
                        <Checkbox
                          checked={getValues('correctAnswer') ? getValues('correctAnswer').includes(answer.id) : false}
                          onChange={e => {
                            const currentCorrectAnswers = getValues('correctAnswer') || []
                            let newCorrectAnswers

                            if (e.target.checked) {
                              newCorrectAnswers = [...currentCorrectAnswers, answer.id]
                              clearErrors('correctAnswer')
                            } else {
                              newCorrectAnswers = currentCorrectAnswers.filter(id => id !== answer.id)
                            }

                            setValue('correctAnswer', newCorrectAnswers, {
                              shouldDirty: true,
                              shouldTouch: true,
                              shouldValidate: true
                            })
                          }}
                        />
                      )}
                      <Box className='flex items-center justify-between w-full'>
                        <Controller
                          name={`answers.${index}.content`}
                          control={control}
                          defaultValue={answer.content || ''}
                          rules={{
                            required: 'This field is required',
                            validate: value => {
                              const stripHtml = typeof value === 'string' ? value.replace(/<[^>]*>/g, '').trim() : ''

                              return stripHtml !== '' || 'This field is required'
                            }
                          }}
                          render={({ field: { ref, ...field } }) => (
                            <Editor
                              {...field}
                              value={field.value?.toString() || ''}
                              onEditorRef={ref}
                              onChange={value => {
                                field.onChange(value)

                                const currentAnswers = [...getValues('answers')]

                                if (!currentAnswers[index]) return

                                setValue(
                                  `answers.${index}`,
                                  {
                                    ...currentAnswers[index],
                                    content: value || ''
                                  },
                                  { shouldDirty: true }
                                )
                              }}
                              optionalToolbar
                              className='w-[95%]'
                            />
                          )}
                        />
                        {index > 1 && (
                          <IconButton
                            onClick={() => {
                              const currentAnswers = [...field.value]
                              const idToRemove = answer.id
                              const filteredAnswers = currentAnswers.filter((_, i) => i !== index)

                              setValue('answers', filteredAnswers, {
                                shouldDirty: true,
                                shouldTouch: true,
                                shouldValidate: true
                              })

                              const currentCorrectAnswers = getValues('correctAnswer') || []

                              if (currentCorrectAnswers.includes(idToRemove)) {
                                setValue(
                                  'correctAnswer',
                                  currentCorrectAnswers.filter(id => id !== idToRemove),
                                  {
                                    shouldDirty: true,
                                    shouldTouch: true,
                                    shouldValidate: true
                                  }
                                )
                              }
                            }}
                          >
                            <i className='ri-delete-bin-6-fill'></i>
                          </IconButton>
                        )}
                      </Box>
                    </Box>
                    {errors?.answers?.[index]?.content && (
                      <Typography className='ml-11 mt-1 text-error' variant='caption'>
                        {errors?.answers?.[index]?.content?.message}
                      </Typography>
                    )}
                  </Grid>
                ))}
                <Grid item xs={12} className='mt-2'>
                  <Button
                    variant='outlined'
                    startIcon={<i className='ri-add-line'></i>}
                    onClick={() => {
                      const currentAnswers = [...getValues('answers').filter(answer => answer.content !== undefined)]

                      const newAnswer = { id: getRandomId(), content: '' }

                      setValue('answers', [...currentAnswers, newAnswer], {
                        shouldDirty: true,
                        shouldTouch: true
                      })

                      if (!getValues('correctAnswer')) {
                        setValue('correctAnswer', [], {
                          shouldDirty: true
                        })
                      }
                    }}
                    className='ml-11'
                  >
                    Add Answer
                  </Button>
                </Grid>
              </>
            )}
          />
          {errors.correctAnswer && (
            <Typography className='ml-11 mt-1 text-error' variant='caption'>
              {errors.correctAnswer.message}
            </Typography>
          )}

          {/* Explanation */}
          <Grid item xs={12} className='pt-4 ml-11'>
            <Typography variant='body1' className='mbe-2 font-bold' color='text.primary'>
              Overall explanation
            </Typography>
            <Box className='flex justify-between'>
              <Controller
                name='explanation'
                control={control}
                rules={{
                  required: 'This field is required',
                  validate: value => {
                    const stripHtml = typeof value === 'string' ? value.replace(/<[^>]*>/g, '').trim() : ''

                    return stripHtml !== '' || 'This field is required'
                  }
                }}
                render={({ field: { ref, ...field } }) => (
                  <Editor {...field} onEditorRef={ref} className='w-[95%]' optionalToolbar />
                )}
              />
              <IconButton className='opacity-0 w-11'>
                <i className='ri-delete-bin-6-fill'></i>
              </IconButton>
            </Box>
            {errors.explanation && (
              <Typography className='mt-1 text-error' variant='caption'>
                {errors.explanation.message}
              </Typography>
            )}
          </Grid>

          {/* Submit Button */}
          <Grid item xs={12} className='pt-4 ml-11'>
            <Button variant='contained' type='submit' loading={loading}>
              Save Question
            </Button>
          </Grid>
        </form>
      </CardContent>

      {/* Delete Question Button */}
      <Button
        className='absolute top-4 right-12'
        startIcon={<i className='ri-delete-bin-6-fill text-error' style={{ fontSize: 20 }}></i>}
        onClick={() => handleDeleteQuestion(getValues('questionId'))}
        color='error'
      >
        Delete
      </Button>
    </Card>
  )
}

export default QuestionEmptyForm
