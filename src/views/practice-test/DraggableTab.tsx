import React from 'react'

// MUI Imports
import { IconButton, Tab } from '@mui/material'

interface DraggableTabProps {
  label: string
  index: number
  onDragStart: (e: React.DragEvent, index: number) => void
  onDragOver: (e: React.DragEvent) => void
  onDrop: (e: React.DragEvent, dropIndex: number) => void
}

const DraggableTab: React.FC<DraggableTabProps> = ({ label, index, onDragStart, onDragOver, onDrop }) => {
  return (
    <Tab
      value={`tab-${index}`}
      label={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%'
          }}
        >
          <span>{label}</span>
          <IconButton size='small' onMouseDown={e => onDragStart(e as unknown as React.DragEvent, index)}>
            <i className='ri-menu-line'></i>
          </IconButton>
        </div>
      }
      onDragOver={onDragOver}
      onDrop={e => onDrop(e, index)}
      draggable
    />
  )
}

export default DraggableTab
