import { useRef } from 'react'

import dynamic from 'next/dynamic'

// MUI Imports
import { Box, Button, Card, CardContent, CardHeader, Grid, Switch, TextField, Typography } from '@mui/material'

import { Controller, useFormContext } from 'react-hook-form'

import type { Exam } from '@/api/exam'

const Editor = dynamic(() => import('@/components/Editor'), {
  ssr: false
})

interface Props {
  title: string
  onSubmit: (data: Exam) => void
  loading?: boolean
}

const FormAdd = ({ title, onSubmit, loading }: Props) => {
  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useFormContext<Exam>()

  const formRef = useRef<HTMLFormElement>(null)

  return (
    <Card className='h-full max-w-[88%] m-auto'>
      <CardHeader title={title} />
      <CardContent>
        <form ref={formRef} onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={5}>
            <Grid item xs={12}>
              <Box className='w-full'>
                <Controller
                  name='examName'
                  control={control}
                  rules={{
                    required: 'This field is required',
                    validate: value => (value.trim() ? true : 'This field is required')
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      onChange={e => {
                        field.onChange(e.target.value)
                      }}
                      label='Exam Name'
                      placeholder='Practice Exams | AWS Certified Solutions Architect Associate'
                      size='small'
                    />
                  )}
                />
                {errors.examName && (
                  <Typography className='mt-1 text-error' variant='caption' color='error'>
                    {errors.examName.message}
                  </Typography>
                )}
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Controller
                name='durationMinutes'
                control={control}
                rules={{
                  required: 'This field is required',
                  max: {
                    value: 1440,
                    message: 'Duration time must be less than 1440 minutes'
                  }
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    onChange={e => {
                      field.onChange(e.target.value)
                    }}
                    label='Duration Time (minute)'
                    type='number'
                    inputProps={{
                      min: 0
                    }}
                    fullWidth
                    size='small'
                  />
                )}
              />
              {errors.durationMinutes && (
                <Typography className='mt-1 text-error' variant='caption' color='error'>
                  {errors.durationMinutes.message}
                </Typography>
              )}
            </Grid>
            <Grid item xs={12}>
              <Box className='w-full'>
                <Controller
                  name='passThreshold'
                  control={control}
                  rules={{
                    required: 'This field is required',
                    max: {
                      value: 100,
                      message: 'Pass rate must be less than 100%'
                    }
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      onChange={e => {
                        field.onChange(e.target.value)
                      }}
                      label='Pass rate (%)'
                      type='number'
                      inputProps={{
                        min: 0,
                        max: 100
                      }}
                      fullWidth
                      size='small'
                    />
                  )}
                />
                {errors.passThreshold && (
                  <Typography className='mt-1 text-error' variant='caption' color='error'>
                    {errors.passThreshold.message}
                  </Typography>
                )}
              </Box>
            </Grid>
            <Grid item xs={12} className='pt-4'>
              <Typography variant='h6' className='mbe-2'>
                Descriptions
              </Typography>
              <Box className='w-full'>
                <Controller
                  name='description'
                  control={control}
                  rules={{
                    required: 'This field is required',
                    validate: value => value !== '<p><br></p>' || 'This field is required'
                  }}
                  render={({ field: { ref, ...field } }) => (
                    <Editor
                      {...field}
                      onEditorRef={ref}
                      onChange={value => {
                        field.onChange(value)
                      }}
                    />
                  )}
                />
                {errors.description && (
                  <Typography className='mt-1 text-error' variant='caption' color='error'>
                    {errors.description.message}
                  </Typography>
                )}
              </Box>
            </Grid>
            <Grid item xs={12} className='pt-4'>
              <div className='flex items-center'>
                <Typography variant='body1' color='text.primary'>
                  Randomize question order
                </Typography>
                <Controller
                  name='isRandomQuestion'
                  control={control}
                  render={({ field }) => (
                    <Switch
                      {...field}
                      size='medium'
                      checked={field.value ?? false}
                      onChange={e => {
                        field.onChange(e.target.checked)
                      }}
                    />
                  )}
                />
              </div>
            </Grid>
            <Grid item xs={12} className='pt-7'>
              <div className='flex items-center justify-between flex-wrap gap-5'>
                <Button variant='contained' type='submit' loading={loading}>
                  Save
                </Button>
              </div>
            </Grid>
          </Grid>
        </form>
      </CardContent>
    </Card>
  )
}

export default FormAdd
