// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'

import { CircularProgress } from '@mui/material'

import { formatNumber } from '@/utils'

const Award = ({ revenue, loading }: { revenue: number; loading: boolean }) => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-[10px] relative items-start'>
        <div>
          <Typography variant='h5'>Congratulations 🎉</Typography>
        </div>
        <div>
          {loading ? (
            <CircularProgress size={24} />
          ) : (
            <Typography variant='h4' color='primary'>
              {`${formatNumber(revenue)} (VND)`}
            </Typography>
          )}
        </div>
        <Button size='small' variant='contained'>
          View Revenue
        </Button>
        <img
          src='/images/pages/trophy.png'
          alt='trophy image'
          height={102}
          className='absolute inline-end-5 bottom-6'
        />
      </CardContent>
    </Card>
  )
}

export default Award
