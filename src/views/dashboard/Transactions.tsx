//MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Grid from '@mui/material/Grid'

// Type Imports
import { CircularProgress } from '@mui/material'

import type { ThemeColor } from '@/core/types'

// Components Imports
import CustomAvatar from '@/core/components/mui/Avatar'
import { formatNumber } from '@/utils'

type DataType = {
  icon: string
  stats: number
  title: string
  color: ThemeColor | string
}

interface Props {
  users?: number
  orders?: number
  courses?: number
  instructors?: number
  loading: boolean
}

const Transactions: React.FC<Props> = ({ users, orders, courses, instructors, loading }) => {
  // Vars
  const data: DataType[] = [
    {
      stats: users || 0,
      title: 'Users',
      color: 'success',
      icon: 'ri-group-fill'
    },
    {
      stats: orders || 0,
      title: 'Orders',
      color: 'warning',
      icon: 'ri-shopping-cart-2-fill'
    },
    {
      stats: courses || 0,
      color: 'info',
      title: 'Courses',
      icon: 'ri-file-text-fill'
    },
    {
      stats: instructors || 0,
      color: 'primary',
      title: 'Instructors',
      icon: 'ri-graduation-cap-fill'
    }
  ]

  return (
    <Card className='bs-full'>
      <CardHeader title='Transactions' />
      {loading ? (
        <CardContent className='flex items-center justify-center'>
          <CircularProgress />
        </CardContent>
      ) : (
        <CardContent className='!pbs-5'>
          <Grid container spacing={2}>
            {data.map((item, index) => (
              <Grid item xs={6} md={3} key={index}>
                <div className='flex items-center gap-3'>
                  <CustomAvatar variant='rounded' color={item.color} className='shadow-xs'>
                    <i className={item.icon}></i>
                  </CustomAvatar>
                  <div>
                    <Typography>{item.title}</Typography>
                    <Typography variant='h5'>{formatNumber(item.stats)}</Typography>
                  </div>
                </div>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      )}
    </Card>
  )
}

export default Transactions
