// MUI Imports
import React from 'react'

import Typography from '@mui/material/Typography'
import Card from '@mui/material/Card'

// Components Imports
import { CircularProgress } from '@mui/material'

import CustomAvatar from '@/core/components/mui/Avatar'

// Styles Imports
import tableStyles from '@/core/styles/table.module.css'
import type { User } from '@/api/user'

interface TableProps {
  users: User[]
  loading: boolean
}

const Table: React.FC<TableProps> = ({ users, loading }) => {
  return (
    <Card>
      <div className='overflow-x-auto'>
        <table className={tableStyles.table}>
          <thead>
            <tr>
              <th>User</th>
              <th>Email</th>
              <th>Register Date</th>
            </tr>
          </thead>
          {loading ? (
            <tbody>
              <tr>
                <td colSpan={3} className='text-center'>
                  <CircularProgress />
                </td>
              </tr>
            </tbody>
          ) : (
            <tbody>
              {users.map((row, index) => (
                <tr key={index}>
                  <td className='!plb-1'>
                    <div className='flex items-center gap-3'>
                      <CustomAvatar src={row?.picture || '/images/avatars/1.png'} size={34} />
                      <div className='flex flex-col'>
                        <Typography color='text.primary' className='font-medium'>
                          {row.firstName} {row.lastName}
                        </Typography>
                        <Typography variant='body2'>{row.firstName}</Typography>
                      </div>
                    </div>
                  </td>
                  <td className='!plb-1'>
                    <Typography>{row.email}</Typography>
                  </td>
                  <td className='!plb-1'>
                    <div className='flex gap-2'>
                      <Typography color='text.primary'>{row.birthdate}</Typography>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          )}
        </table>
      </div>
    </Card>
  )
}

export default Table
