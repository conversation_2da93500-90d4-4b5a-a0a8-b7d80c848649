import React from 'react'

import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TableSortLabel
} from '@mui/material'

interface TableColumn {
  id: string
  label: string
  width?: string
  fixed: boolean
  sortable?: boolean // Thêm option để sort
}

interface SortConfig {
  key: string
  direction: 'asc' | 'desc'
}

interface TableProps {
  data: any[]
  columns: TableColumn[]
  selectedColumns: string[]
  page: number
  rowsPerPage: number

  // Sort props
  sortConfig?: SortConfig
  onSort?: (key: string, direction: 'asc' | 'desc') => void

  // Pagination handlers
  handleChangePage: (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => void
  handleChangeRowsPerPage: (event: React.ChangeEvent<HTMLInputElement>) => void

  // Render customization
  renderCell?: (columnId: string, rowData: any) => React.ReactNode
  actions?: React.ReactNode | ((rowId: number) => React.ReactNode)
  actionType?: 'static' | 'dynamic'
  onActionClick?: (id: number) => void

  // Custom styling
  className?: string
  headerClassName?: string
  rowClassName?: string
  cellClassName?: string
}

const TableCustom: React.FC<TableProps> = ({
  data,
  columns,
  selectedColumns,
  page,
  rowsPerPage,
  sortConfig,
  onSort,
  handleChangePage,
  handleChangeRowsPerPage,
  renderCell,
  actions,
  actionType = 'static',
  onActionClick,
  className = '',
  headerClassName = '',
  rowClassName = '',
  cellClassName = ''
}) => {
  const handleSort = (columnId: string) => {
    if (!onSort) return

    const direction = sortConfig?.key === columnId && sortConfig.direction === 'asc' ? 'desc' : 'asc'

    onSort(columnId, direction)
  }

  const renderSortLabel = (column: TableColumn) => {
    if (!column.sortable) return column.label

    return (
      <TableSortLabel
        active={sortConfig?.key === column.id}
        direction={sortConfig?.key === column.id ? sortConfig.direction : 'asc'}
        onClick={() => handleSort(column.id)}
        sx={{
          '& .MuiTableSortLabel-icon': {
            opacity: 1
          }
        }}
      >
        {column.label}
      </TableSortLabel>
    )
  }

  const renderActions = (rowId: number): React.ReactNode => {
    if (!actions) return null

    if (actionType === 'dynamic' && typeof actions === 'function') {
      return actions(rowId)
    }

    if (onActionClick) {
      return (
        <div onClick={() => onActionClick(rowId)} className='cursor-pointer'>
          {actions as React.ReactNode}
        </div>
      )
    }

    return actions as React.ReactNode
  }

  return (
    <>
      <TableContainer className={`border border-solid rounded ${className}`}>
        <Table className='w-full table-auto'>
          <TableHead>
            <TableRow className={headerClassName}>
              {columns.map(
                col =>
                  selectedColumns.includes(col.id) && (
                    <TableCell
                      key={col.id}
                      width={col.width}
                      className={`px-4 py-2 border-b text-base whitespace-nowrap ${cellClassName}`}
                    >
                      {renderSortLabel(col)}
                    </TableCell>
                  )
              )}
              {actions && <TableCell className='px-4 py-2 border-b'></TableCell>}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map(row => (
              <TableRow key={row.id} className={rowClassName}>
                {columns.map(
                  col =>
                    selectedColumns.includes(col.id) && (
                      <TableCell
                        width={col.width}
                        className={`px-4 py-2 border-b text-base ${cellClassName}`}
                        key={col.id}
                      >
                        {renderCell ? renderCell(col.id, row) : row[col.id]}
                      </TableCell>
                    )
                )}
                {actions && <TableCell className='cursor-pointer'>{renderActions(row.id)}</TableCell>}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[10, 25, 50]}
        component='div'
        count={data.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </>
  )
}

export default TableCustom
