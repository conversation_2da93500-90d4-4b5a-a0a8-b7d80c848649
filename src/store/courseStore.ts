import { create } from 'zustand'

// Utils
import { getRandomId } from '@/utils'

// Data
// import { questions } from '@/data'
import type { Exam } from '@/api/exam'
import type { Question } from '@/api/question'

interface TypeState {
  type: string
  addType?: string
}

interface Course {
  questions: Question[]
  practicePlan: Exam
}

interface CourseStore {
  course: Course
  setCourse: (course: Course) => void
  originalPracticePlan: Exam | null
  hasPracticePlanChanged: boolean

  updatePracticePlan: (updates: Partial<Exam>) => void
  checkPracticePlanChanges: (currentData: Exam) => boolean
  setOriginalPracticePlan: (plan: Exam | null) => void
  resetPracticePlanChanges: () => void

  isSubmitPlan: boolean
  setIsSubmitPlan: (newState: boolean) => void

  // Actions for managing answers
  addAnswer: (questionId: number, answer: string[]) => void
  updateAnswer: (questionId: number, updatedAnswer: string[]) => void

  // Actions for managing questions
  addQuestion: (question: Question) => void
  updateQuestion: (questionId: number, updatedQuestion: Partial<Question>) => void
  deleteQuestion: (questionId: number) => void

  // Current question management
  currentQuestionId: number | null
  currentQuestionIndex: number
  setCurrentQuestion: (questionId: number) => void
  getCurrentQuestion: () => Question | null
  reorderQuestions: (newOrder: Question[]) => void

  // Question change tracking
  originalQuestion: Question | null
  hasQuestionChanged: boolean
  setOriginalQuestion: (question: Question | null) => void
  checkQuestionChanges: (currentData: Question) => boolean
  resetQuestionChanges: () => void

  // Check state to submit
  isSubmit: boolean
  setIsSubmit: (newState: boolean) => void

  nextTab: number
  setNextTab: (newState: number) => void

  actionType: TypeState
  setActionType: (newState: TypeState) => void

  isNewQuestion: boolean
  setIsNewQuestion: (newState: boolean) => void

  isDiscardChanges: boolean
  setIsDiscardChanges: (newState: boolean) => void
}

// Create store
const useCourseStore = create<CourseStore>((set, get) => ({
  // Initial state
  course: {
    questions: [],
    practicePlan: {
      examId: 0,
      examName: '',
      courseId: 0,
      isRandomQuestion: false,
      createdAt: '',
      updatedAt: '',
      description: '',
      questionCount: 0,
      currentVersionId: 0,
      durationMinutes: 0,
      passThreshold: 0,
      publishDate: ''
    }
  },

  setCourse: newCourse => set({ course: newCourse }),

  // Add state for tracking changes
  originalQuestion: null,
  hasQuestionChanged: false,

  // Add a new question
  addQuestion: questionData =>
    set(state => {
      const newQuestion = {
        ...questionData,
        answers:
          questionData.answers?.map((answer: any) => ({
            ...answer,
            id: getRandomId()
          })) || []
      }

      const updatedQuestions = [...state.course.questions, newQuestion]

      // Update state including the new question and setting it as current
      return {
        course: {
          ...state.course,
          questions: updatedQuestions
        },
        currentQuestionId: newQuestion.questionId,
        currentQuestionIndex: updatedQuestions.length - 1
      }
    }),

  // Update an existing question
  updateQuestion: (questionId, updatedQuestion) =>
    set(state => ({
      course: {
        ...state.course,
        questions: state.course.questions.map(question =>
          question.questionId === questionId ? { ...question, ...updatedQuestion } : question
        )
      }
    })),

  // Delete a question
  deleteQuestion: async (questionId: number) =>
    new Promise<void>(resolve => {
      set(state => ({
        course: {
          ...state.course,
          questions: state.course.questions.filter(question => question.questionId !== questionId)
        }
      }))
      resolve()
    }),

  // Add a new answer to a question
  addAnswer: (questionId, answerData) =>
    set(state => ({
      course: {
        ...state.course,
        questions: state.course.questions.map(question =>
          question.questionId === questionId
            ? {
                ...question,
                answers: [...answerData]
              }
            : question
        )
      }
    })),

  // Update an existing answer
  updateAnswer: (questionId, updatedAnswer) =>
    set(state => ({
      course: {
        ...state.course,
        questions: state.course.questions.map(question =>
          question.questionId === questionId
            ? {
                ...question,
                answers: updatedAnswer
              }
            : question
        )
      }
    })),

  // Current question management
  currentQuestionId: null,
  currentQuestionIndex: 0,

  // Set current question by ID
  setCurrentQuestion: (questionId: number) =>
    set(state => {
      const index = state.course.questions.findIndex(q => q.questionId === questionId)

      return {
        currentQuestionId: questionId,
        currentQuestionIndex: index >= 0 ? index : state.currentQuestionIndex
      }
    }),

  // Get current question
  getCurrentQuestion: () => {
    const state = get()

    return state.course.questions[state.currentQuestionIndex] || null
  },

  // Add new action for reordering questions
  reorderQuestions: (newOrder: Question[]) =>
    set(state => ({
      course: {
        ...state.course,
        questions: newOrder
      }
    })),

  // Set original question data when starting to edit
  setOriginalQuestion: (question: Question | null) =>
    set({
      originalQuestion: question ? { ...question } : null,
      hasQuestionChanged: false
    }),

  // Check if current question data differs from original
  checkQuestionChanges: (currentData: Question) => {
    const state = get()
    const original = state.originalQuestion

    if (!original) return false

    // Deep comparison of questions
    const hasChanged =
      original.questionText !== currentData.questionText ||
      original.explanation !== currentData.explanation ||
      !areAnswersEqual(original.answers, currentData.answers)

    set({ hasQuestionChanged: hasChanged })

    return hasChanged
  },

  // Reset change tracking
  resetQuestionChanges: () =>
    set({
      originalQuestion: null,
      hasQuestionChanged: false
    }),

  // Check state to submit
  isSubmit: false,
  setIsSubmit: (newState: boolean) => set({ isSubmit: newState }),

  nextTab: 0,
  setNextTab: (newState: number) => set({ nextTab: newState }),

  actionType: {
    type: ''
  },
  setActionType: (newState: TypeState) => set({ actionType: newState }),

  isNewQuestion: false,
  setIsNewQuestion: (newState: boolean) => set({ isNewQuestion: newState }),

  isDiscardChanges: false,
  setIsDiscardChanges: (newState: boolean) => set({ isDiscardChanges: newState }),

  // Practice Plan change tracking
  originalPracticePlan: null,
  hasPracticePlanChanged: false,

  updatePracticePlan: updates =>
    set(state => ({
      course: {
        ...state.course,
        practicePlan: {
          ...state.course.practicePlan,
          ...updates
        }
      }
    })),

  setOriginalPracticePlan: plan =>
    set({
      originalPracticePlan: plan ? { ...plan } : null,
      hasPracticePlanChanged: false
    }),

  checkPracticePlanChanges: currentData => {
    const state = get()
    const original = state.originalPracticePlan

    if (!original) return false

    const hasChanged =
      original.examName !== currentData.examName ||
      original.durationMinutes !== currentData.durationMinutes ||
      original.passThreshold !== currentData.passThreshold ||
      original.description !== currentData.description ||
      original.isRandomQuestion !== currentData.isRandomQuestion

    set({ hasPracticePlanChanged: hasChanged })

    return hasChanged
  },

  resetPracticePlanChanges: () =>
    set({
      originalPracticePlan: null,
      hasPracticePlanChanged: false
    }),

  isSubmitPlan: false,
  setIsSubmitPlan: (newState: boolean) => set({ isSubmitPlan: newState })
}))

// Helper function to compare answers arrays
const areAnswersEqual = (original: string[], current: string[]): boolean => {
  if (original.length !== current.length) return false

  return original.every((originalAnswer, index) => {
    const currentAnswer = current[index]

    return originalAnswer === currentAnswer && originalAnswer === currentAnswer
  })
}

export default useCourseStore
