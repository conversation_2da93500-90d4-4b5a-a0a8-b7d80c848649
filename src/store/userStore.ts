import { create } from 'zustand'

import type { User } from '@/api/user'

interface UserStore {
  users: User[]
  hasChanges: boolean
  setUsers: (users: User[]) => void
  addUser: (user: User) => void
  removeUser: (id: number) => void
  resetChanges: () => void
}

const useUserStore = create<UserStore>(set => ({
  users: [],
  hasChanges: false,

  setUsers: (users: User[]) =>
    set(() => ({
      users,
      hasChanges: false
    })),

  addUser: (user: User) =>
    set(state => ({
      users: [...state.users, user],
      hasChanges: true
    })),

  removeUser: (id: number) =>
    set(state => ({
      users: state.users.filter(user => user.userId !== id),
      hasChanges: true
    })),

  resetChanges: () =>
    set(() => ({
      hasChanges: false
    }))
}))

export default useUserStore
